{"cells": [{"cell_type": "markdown", "id": "753fce2b", "metadata": {}, "source": ["# Análise Territorial Abrangente – <PERSON>lli<PERSON><PERSON> (Vanessa)\n", "\n", "**Objetivo:** Comparar abordagens supervisionadas e não-supervisionadas para ranquear regiões por potencial de expansão.\n", "\n", "Este notebook demonstra ambas as metodologias, permitindo à equipe escolher a estratégia mais adequada conforme o contexto de negócio."]}, {"cell_type": "markdown", "id": "9e10bd46", "metadata": {}, "source": ["## 1. <PERSON><PERSON><PERSON><PERSON> Exploratória Geográfica"]}, {"cell_type": "code", "execution_count": 1, "id": "d174b8e0", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:04.045238Z", "iopub.status.busy": "2025-09-11T19:52:04.045238Z", "iopub.status.idle": "2025-09-11T19:52:06.993167Z", "shell.execute_reply": "2025-09-11T19:52:06.993167Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Carregando dados de: ..\\data\\processed\\features_engineered.csv\n"]}], "source": ["from pathlib import Path\n", "import pandas as pd, numpy as np, json\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.cluster import KMeans\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import KFold, cross_validate\n", "from sklearn.metrics import silhouette_score\n", "import warnings; warnings.filterwarnings('ignore')\n", "import joblib\n", "BASE = Path('.')\n", "if not (BASE/'data'/'processed'/'features_engineered.csv').exists(): BASE = Path('..')\n", "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n", "REPORTS = BASE/'reports'/'2025-08-15'/'territorial_analysis'\n", "MODELS = BASE/'models'\n", "for d in [REPORTS, MODELS]: d.mkdir(parents=True, exist_ok=True)\n", "print('Carregando dados de:', DATA)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "fce94ec8", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:06.996138Z", "iopub.status.busy": "2025-09-11T19:52:06.995131Z", "iopub.status.idle": "2025-09-11T19:52:07.992782Z", "shell.execute_reply": "2025-09-11T19:52:07.992782Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (35616, 182)\n", "Colunas regionais disponíveis: ['Dim_<PERSON><PERSON>.Cod_Franqueado', 'cidade_freq', 'cidade_was_missing', 'uf_was_missing', 'Dim_Cliente.Regiao_Cliente_was_missing', 'Dim_<PERSON><PERSON>.Bairro_Emp_was_missing', 'Dim_<PERSON>jas.ID_SAP_was_missing', 'Dim_Lojas.Cod_Franqueado_was_missing', 'cidade_freq_was_missing', 'Tipo_PDV_LOJA                '] ...\n", "Chave de agregação selecionada: grupo_sintetico (cardinalidade: 9)\n"]}], "source": ["# Carregamento e preparação inicial\n", "df = pd.read_csv(DATA, low_memory=False)\n", "print('Dataset shape:', df.shape)\n", "# Identificar colunas regionais disponíveis (buscar por padrões)\n", "regional_candidates = ['regiao','uf','cidade','bairro','latitude','longitude','Tipo_PDV']\n", "# Buscar por colunas que contenham padrões regionais\n", "regional_patterns = ['regiao', 'uf', 'cidade', 'tipo_pdv', 'canal', 'loja']\n", "available_regional = []\n", "for col in df.columns:\n", "    col_lower = col.lower().replace('_', '').replace('.', '')\n", "    if any(pattern in col_lower for pattern in regional_patterns):\n", "        available_regional.append(col)\n", "print('Colunas regionais disponíveis:', available_regional[:10], '...' if len(available_regional)>10 else '')\n", "# Definir chave de agregação (priorizar colunas categóricas com boa cardinalidade)\n", "GROUP_KEY = None\n", "for col in available_regional:\n", "    if df[col].dtype == 'object' or (df[col].dtype in ['int64','float64'] and df[col].nunique() < len(df)/10):\n", "        if df[col].nunique() > 5 and df[col].nunique() < 100:  # Boa cardinalidade\n", "            GROUP_KEY = col; break\n", "# Fallback: usar primeira coluna categórica disponível\n", "if not GROUP_KEY:\n", "    cat_cols = [c for c in df.columns if df[c].dtype == 'object' and df[c].nunique() > 3]\n", "    if cat_cols:\n", "        GROUP_KEY = cat_cols[0]\n", "    else:\n", "        # Último fallback: criar grupos sintéticos baseados em percentis de valor\n", "        try:\n", "            df['grupo_sintetico'] = pd.qcut(df['valor'], q=10, labels=False, duplicates='drop')\n", "            df['grupo_sintetico'] = 'Grupo_' + (df['grupo_sintetico'] + 1).astype(str)\n", "        except Exception:\n", "            # Se qcut falhar, usar binning simples\n", "            df['grupo_sintetico'] = pd.cut(df['valor'], bins=10, labels=False)\n", "            df['grupo_sintetico'] = 'Grupo_' + (df['grupo_sintetico'] + 1).astype(str)\n", "        GROUP_KEY = 'grupo_sintetico'\n", "print('Chave de agregação selecionada:', GROUP_KEY, f'(cardinalidade: {df[GROUP_KEY].nunique()})')\n"]}, {"cell_type": "code", "execution_count": 3, "id": "09b7e238", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:07.996696Z", "iopub.status.busy": "2025-09-11T19:52:07.995719Z", "iopub.status.idle": "2025-09-11T19:52:08.032655Z", "shell.execute_reply": "2025-09-11T19:52:08.031785Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dados regionais agregados: (9, 12)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grupo_sintetico</th>\n", "      <th>valor_sum</th>\n", "      <th>valor_mean</th>\n", "      <th>valor_median</th>\n", "      <th>valor_count</th>\n", "      <th>valor_std</th>\n", "      <th>qtd_sum</th>\n", "      <th>qtd_mean</th>\n", "      <th>Preco_Custo_sum</th>\n", "      <th>Preco_Custo_mean</th>\n", "      <th>Desconto_sum</th>\n", "      <th>Desconto_mean</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Grupo_1</td>\n", "      <td>-3952.370075</td>\n", "      <td>-0.908173</td>\n", "      <td>-0.901927</td>\n", "      <td>4352</td>\n", "      <td>0.013919</td>\n", "      <td>187.0</td>\n", "      <td>0.042969</td>\n", "      <td>-4124.817002</td>\n", "      <td>-0.947798</td>\n", "      <td>67521.62</td>\n", "      <td>15.515078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Grupo_2</td>\n", "      <td>-2562.250071</td>\n", "      <td>-0.873594</td>\n", "      <td>-0.870281</td>\n", "      <td>2933</td>\n", "      <td>0.010635</td>\n", "      <td>55.0</td>\n", "      <td>0.018752</td>\n", "      <td>-3069.678419</td>\n", "      <td>-1.046600</td>\n", "      <td>28179.66</td>\n", "      <td>9.607794</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Grupo_3</td>\n", "      <td>-2255.094940</td>\n", "      <td>-0.662679</td>\n", "      <td>-0.683471</td>\n", "      <td>3403</td>\n", "      <td>0.148454</td>\n", "      <td>696.0</td>\n", "      <td>0.204525</td>\n", "      <td>-1049.190629</td>\n", "      <td>-0.308313</td>\n", "      <td>41844.76</td>\n", "      <td>12.296433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Grupo_4</td>\n", "      <td>-1099.864711</td>\n", "      <td>-0.308951</td>\n", "      <td>-0.316466</td>\n", "      <td>3560</td>\n", "      <td>0.050752</td>\n", "      <td>209.0</td>\n", "      <td>0.058708</td>\n", "      <td>-145.986569</td>\n", "      <td>-0.041007</td>\n", "      <td>106494.99</td>\n", "      <td>29.914323</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Grupo_5</td>\n", "      <td>-171.018355</td>\n", "      <td>-0.043023</td>\n", "      <td>0.000000</td>\n", "      <td>3975</td>\n", "      <td>0.054857</td>\n", "      <td>252.0</td>\n", "      <td>0.063396</td>\n", "      <td>337.282204</td>\n", "      <td>0.084851</td>\n", "      <td>99746.36</td>\n", "      <td>25.093424</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  grupo_sintetico    valor_sum  valor_mean  valor_median  valor_count  \\\n", "0         Grupo_1 -3952.370075   -0.908173     -0.901927         4352   \n", "1         Grupo_2 -2562.250071   -0.873594     -0.870281         2933   \n", "2         Grupo_3 -2255.094940   -0.662679     -0.683471         3403   \n", "3         Grupo_4 -1099.864711   -0.308951     -0.316466         3560   \n", "4         Grupo_5  -171.018355   -0.043023      0.000000         3975   \n", "\n", "   valor_std  qtd_sum  qtd_mean  Preco_Custo_sum  Preco_Custo_mean  \\\n", "0   0.013919    187.0  0.042969     -4124.817002         -0.947798   \n", "1   0.010635     55.0  0.018752     -3069.678419         -1.046600   \n", "2   0.148454    696.0  0.204525     -1049.190629         -0.308313   \n", "3   0.050752    209.0  0.058708      -145.986569         -0.041007   \n", "4   0.054857    252.0  0.063396       337.282204          0.084851   \n", "\n", "   Desconto_sum  Desconto_mean  \n", "0      67521.62      15.515078  \n", "1      28179.66       9.607794  \n", "2      41844.76      12.296433  \n", "3     106494.99      29.914323  \n", "4      99746.36      25.093424  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Agregações regionais para análise\n", "agg_dict = {\n", "    'valor': ['sum','mean','median','count','std'],\n", "}\n", "# Adicionar outras métricas se disponíveis\n", "for col in ['qtd', 'Preco_Custo', 'Descon<PERSON>']:\n", "    if col in df.columns:\n", "        agg_dict[col] = ['sum','mean']\n", "# Realizar agregação\n", "regional_data = df.groupby(GROUP_KEY).agg(agg_dict)\n", "regional_data.columns = ['_'.join([a,b]) for a,b in regional_data.columns]\n", "regional_data = regional_data.reset_index()\n", "print('Dados regionais agregados:', regional_data.shape)\n", "# Adicionar coordenadas médias se disponíveis\n", "if {'latitude','longitude'}.issubset(df.columns):\n", "    coords = df.groupby(GROUP_KEY)[['latitude','longitude']].mean().reset_index()\n", "    regional_data = regional_data.merge(coords, on=GROUP_KEY, how='left')\n", "regional_data.head()\n"]}, {"cell_type": "markdown", "id": "0553ff31", "metadata": {}, "source": ["### 1.1 Mapa Exploratório de Vendas"]}, {"cell_type": "code", "execution_count": 4, "id": "be8d0ca3", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:08.035734Z", "iopub.status.busy": "2025-09-11T19:52:08.035070Z", "iopub.status.idle": "2025-09-11T19:52:08.048469Z", "shell.execute_reply": "2025-09-11T19:52:08.047485Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Folium não disponível - pulando map<PERSON>mento\n"]}], "source": ["# Mapa de vendas por região usando folium\n", "try:\n", "    import folium\n", "    center = [-14.2350, -51.9253]  # Centro do Brasil\n", "    m_exploratory = folium.Map(location=center, zoom_start=4)\n", "    if {'latitude','longitude'}.issubset(regional_data.columns):\n", "        for _, row in regional_data.iterrows():\n", "            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):\n", "                receita = row.get('valor_sum', 0)\n", "                radius = max(5, min(20, receita/10000))  # Escala do círculo\n", "                folium.CircleMarker(\n", "                    [row['latitude'], row['longitude']],\n", "                    radius=radius,\n", "                    color='blue',\n", "                    fill=True,\n", "                    popup=f\"{row[GROUP_KEY]}: R$ {receita:,.0f}\"\n", "                ).add_to(m_exploratory)\n", "        m_exploratory.save(str(REPORTS/'mapa_exploratorio.html'))\n", "        print('Mapa exploratório salvo em:', REPORTS/'mapa_exploratorio.html')\n", "    else:\n", "        print('Coordenadas não disponíveis para mapeamento')\n", "except ImportError:\n", "    print('Folium não disponível - pulando mapeamento')\n"]}, {"cell_type": "markdown", "id": "68d88c94", "metadata": {}, "source": ["### 1.2 Preparação de Features para Modelagem"]}, {"cell_type": "code", "execution_count": 5, "id": "ec600d2c", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:08.051437Z", "iopub.status.busy": "2025-09-11T19:52:08.051437Z", "iopub.status.idle": "2025-09-11T19:52:08.066753Z", "shell.execute_reply": "2025-09-11T19:52:08.066753Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features para modelagem: ['valor_mean', 'valor_median', 'valor_count', 'valor_std', 'qtd_sum', 'qtd_mean', 'Preco_Custo_sum', 'Preco_Custo_mean', 'Desconto_sum', 'Desconto_mean']\n", "Target (receita): min=-3952, max=2973, mean=-327\n"]}], "source": ["# Selecionar features numéricas para modelagem\n", "feature_cols = [c for c in regional_data.columns if c != GROUP_KEY and pd.api.types.is_numeric_dtype(regional_data[c])]\n", "# Remover coordenadas das features (usar apenas para visualização)\n", "feature_cols = [c for c in feature_cols if c not in ['latitude','longitude']]\n", "# Preparar matriz de features\n", "X_raw = regional_data[feature_cols].fillna(regional_data[feature_cols].median())\n", "# Definir target para abordagem supervisionada\n", "if 'valor_sum' in regional_data.columns:\n", "    y_raw = regional_data['valor_sum'].values\n", "    X_features = X_raw.drop(columns=['valor_sum'], errors='ignore')\n", "else:\n", "    # Fallback: usar primeira coluna numérica como proxy\n", "    y_raw = X_raw.iloc[:,0].values\n", "    X_features = X_raw.iloc[:,1:]\n", "print('Features para modelagem:', X_features.columns.tolist())\n", "print('Target (receita):', f'min={y_raw.min():.0f}, max={y_raw.max():.0f}, mean={y_raw.mean():.0f}')\n"]}, {"cell_type": "markdown", "id": "d100b7e3", "metadata": {}, "source": ["## 2. Abordagem Supervisionada - Modelo Preditivo"]}, {"cell_type": "code", "execution_count": 6, "id": "9ac0a95f", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:08.070383Z", "iopub.status.busy": "2025-09-11T19:52:08.069401Z", "iopub.status.idle": "2025-09-11T19:52:10.740043Z", "shell.execute_reply": "2025-09-11T19:52:10.740043Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Métricas Supervisionadas (CV 5-fold):\n", "  rmse: -1669.3370 ± 626.1552\n", "  mae: -1505.8784 ± 594.2421\n", "  r2: nan ± nan\n"]}], "source": ["# Normalizar features para modelagem\n", "scaler_sup = StandardScaler()\n", "X_scaled = scaler_sup.fit_transform(X_features)\n", "# Configurar modelo Random Forest\n", "rf_model = RandomForestRegressor(\n", "    n_estimators=400,\n", "    max_depth=10,\n", "    min_samples_split=5,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "# Validação cruzada 5-fold\n", "cv = KFold(n_splits=5, shuffle=True, random_state=42)\n", "scoring = {'rmse':'neg_root_mean_squared_error','mae':'neg_mean_absolute_error','r2':'r2'}\n", "cv_results = cross_validate(rf_model, X_scaled, y_raw, cv=cv, scoring=scoring, n_jobs=1)\n", "# Comp<PERSON>r mé<PERSON>\n", "metrics_sup = {}\n", "for metric, scores in cv_results.items():\n", "    if metric.startswith('test_'):\n", "        name = metric.replace('test_', '')\n", "        values = -scores if 'neg_' in metric else scores\n", "        metrics_sup[name] = {'mean': float(np.mean(values)), 'std': float(np.std(values))}\n", "print('Métricas Supervisionadas (CV 5-fold):')\n", "for name, stats in metrics_sup.items():\n", "    print(f'  {name}: {stats[\"mean\"]:.4f} ± {stats[\"std\"]:.4f}')\n"]}, {"cell_type": "code", "execution_count": 7, "id": "1c4b9e86", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:10.742626Z", "iopub.status.busy": "2025-09-11T19:52:10.742626Z", "iopub.status.idle": "2025-09-11T19:52:11.305165Z", "shell.execute_reply": "2025-09-11T19:52:11.304320Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top 10 Regiões - Abordagem Supervisionada:\n", "   rank_supervisionado grupo_sintetico  score_supervisionado\n", "0                    1         Grupo_9             10.000000\n", "1                    2         Grupo_8              9.765741\n", "2                    3         Grupo_6              9.523044\n", "3                    4         Grupo_7              9.499156\n", "4                    5         Grupo_5              6.908325\n", "5                    6         Grupo_4              3.338024\n", "6                    7         Grupo_3              0.376502\n", "7                    8         Grupo_2              0.077041\n", "8                    9         Grupo_1              0.000000\n"]}], "source": ["# Treinar modelo final e gerar scores 0-10\n", "rf_model.fit(X_scaled, y_raw)\n", "y_pred = rf_model.predict(X_scaled)\n", "# Normalizar para score 0-10\n", "score_scaler = MinMaxScaler(feature_range=(0, 10))\n", "scores_supervised = score_scaler.fit_transform(y_pred.reshape(-1, 1)).ravel()\n", "# Criar ranking supervisionado\n", "ranking_sup = regional_data[[GROUP_KEY]].copy()\n", "ranking_sup['receita_real'] = y_raw\n", "ranking_sup['receita_pred'] = y_pred\n", "ranking_sup['score_supervisionado'] = scores_supervised\n", "ranking_sup = ranking_sup.sort_values('score_supervisionado', ascending=False).reset_index(drop=True)\n", "ranking_sup['rank_supervisionado'] = range(1, len(ranking_sup) + 1)\n", "# Salvar ranking\n", "ranking_sup.to_csv(REPORTS/'ranking_supervisionado.csv', index=False)\n", "print('Top 10 Regiões - Abordagem Supervisionada:')\n", "print(ranking_sup[['rank_supervisionado', GROUP_KEY, 'score_supervisionado']].head(10))\n"]}, {"cell_type": "markdown", "id": "273c72c5", "metadata": {}, "source": ["## 3. Abordagem Não-Supervisionada - Segmentação Territorial"]}, {"cell_type": "code", "execution_count": 8, "id": "f032db73", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:11.308102Z", "iopub.status.busy": "2025-09-11T19:52:11.308102Z", "iopub.status.idle": "2025-09-11T19:52:12.150758Z", "shell.execute_reply": "2025-09-11T19:52:12.150758Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K=3: Inertia=38.43, <PERSON><PERSON><PERSON><PERSON>=0.248\n", "K=4: Inertia=26.45, <PERSON><PERSON><PERSON><PERSON>=0.264\n", "K=5: Inertia=10.74, <PERSON><PERSON><PERSON><PERSON>=0.331\n"]}, {"name": "stdout", "output_type": "stream", "text": ["K=6: Inertia=4.90, <PERSON><PERSON><PERSON><PERSON>=0.263\n", "K=7: Inertia=1.95, <PERSON><PERSON><PERSON><PERSON>=0.240\n", "K=8: Inertia=0.48, <PERSON><PERSON><PERSON><PERSON>=0.121\n", "\n", "Mel<PERSON> K selecionado: 5 (<PERSON><PERSON><PERSON><PERSON>: 0.331)\n"]}], "source": ["# Normalizar features para clustering\n", "scaler_unsup = StandardScaler()\n", "X_scaled_unsup = scaler_unsup.fit_transform(X_features)\n", "# Método do cotovelo + Sil<PERSON>ette para seleção de K\n", "k_range = range(3, 9)\n", "inertias = []\n", "silhouette_scores = []\n", "for k in k_range:\n", "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "    labels = kmeans.fit_predict(X_scaled_unsup)\n", "    inertias.append(kmeans.inertia_)\n", "    sil_score = silhouette_score(X_scaled_unsup, labels)\n", "    silhouette_scores.append(sil_score)\n", "    print(f'K={k}: Inertia={kmeans.inertia_:.2f}, Silhouette={sil_score:.3f}')\n", "# Selecionar melhor K por silhouette\n", "best_k = k_range[np.argmax(silhouette_scores)]\n", "best_silhouette = max(silhouette_scores)\n", "print(f'\\nMelhor K selecionado: {best_k} (<PERSON><PERSON>houette: {best_silhouette:.3f})')\n"]}, {"cell_type": "code", "execution_count": 9, "id": "a8e879a9", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.155411Z", "iopub.status.busy": "2025-09-11T19:52:12.155411Z", "iopub.status.idle": "2025-09-11T19:52:12.239446Z", "shell.execute_reply": "2025-09-11T19:52:12.239446Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estatísticas por Cluster:\n", "         valor_sum_count  valor_sum_mean  valor_sum_sum\n", "cluster                                                \n", "0                      2        -3257.31       -6514.62\n", "1                      4          119.29         477.17\n", "2                      1         2372.98        2372.98\n", "3                      1         2972.50        2972.50\n", "4                      1        -2255.09       -2255.09\n", "\n", "Top 10 Regiões por Cluster (ordenado por performance do cluster):\n", "  grupo_sintetico  cluster  cluster_rank\n", "8         Grupo_9        3             1\n", "6         Grupo_7        2             2\n", "7         Grupo_8        1             3\n", "5         Grupo_6        1             3\n", "4         Grupo_5        1             3\n", "3         Grupo_4        1             3\n", "2         Grupo_3        4             4\n", "1         Grupo_2        0             5\n", "0         Grupo_1        0             5\n"]}], "source": ["# Treinar modelo final K-Means\n", "kmeans_final = KMeans(n_clusters=best_k, random_state=42, n_init=10)\n", "cluster_labels = kmeans_final.fit_predict(X_scaled_unsup)\n", "# Analisar clusters\n", "cluster_analysis = regional_data.copy()\n", "cluster_analysis['cluster'] = cluster_labels\n", "# Estatísticas por cluster\n", "if 'valor_sum' in cluster_analysis.columns:\n", "    agg_dict = {'valor_sum': ['count', 'mean', 'sum']}\n", "else:\n", "    agg_dict = {GROUP_KEY: 'count'}\n", "cluster_stats = cluster_analysis.groupby('cluster').agg(agg_dict).round(2)\n", "cluster_stats.columns = ['_'.join(col).strip() for col in cluster_stats.columns]\n", "print('Estatísticas por Cluster:')\n", "print(cluster_stats)\n", "# Criar ranking por cluster (baseado na receita média do cluster)\n", "if 'valor_sum' in cluster_analysis.columns:\n", "    cluster_performance = cluster_analysis.groupby('cluster')['valor_sum'].mean().sort_values(ascending=False)\n", "    cluster_ranking = {cluster: rank+1 for rank, cluster in enumerate(cluster_performance.index)}\n", "else:\n", "    cluster_ranking = {i: i+1 for i in range(best_k)}\n", "cluster_analysis['cluster_rank'] = cluster_analysis['cluster'].map(cluster_ranking)\n", "cluster_analysis = cluster_analysis.sort_values(['cluster_rank', 'valor_sum'], ascending=[True, False])\n", "# Salvar resultados\n", "cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']].to_csv(REPORTS/'clusters_nao_supervisionado.csv', index=False)\n", "print('\\nTop 10 Regiões por Cluster (ordenado por performance do cluster):')\n", "print(cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']].head(10))\n"]}, {"cell_type": "markdown", "id": "4acedee6", "metadata": {}, "source": ["## 4. Comparação Estratégica Entre Abordagens"]}, {"cell_type": "code", "execution_count": 10, "id": "588505fc", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.243361Z", "iopub.status.busy": "2025-09-11T19:52:12.242384Z", "iopub.status.idle": "2025-09-11T19:52:12.263081Z", "shell.execute_reply": "2025-09-11T19:52:12.262544Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Taxa de concordância entre métodos: 88.89%\n", "\n", "Regiões priorizadas por AMBOS os métodos:\n", "  grupo_sintetico  rank_supervisionado  cluster_rank  score_supervisionado\n", "0         Grupo_9                    1             1             10.000000\n", "1         Grupo_8                    2             3              9.765741\n", "2         Grupo_6                    3             3              9.523044\n", "3         Grupo_7                    4             2              9.499156\n", "4         Grupo_5                    5             3              6.908325\n", "5         Grupo_4                    6             3              3.338024\n"]}], "source": ["# Merge dos resultados para comparação\n", "comparison = ranking_sup[[GROUP_KEY, 'rank_supervisionado', 'score_supervisionado']].merge(\n", "    cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']], on=GROUP_KEY, how='inner'\n", ")\n", "# Análise de concordância\n", "comparison['rank_diff'] = abs(comparison['rank_supervisionado'] - comparison['cluster_rank'])\n", "comparison['concordancia'] = comparison['rank_diff'] <= 3  # Concordância se diferença <= 3 posições\n", "concordancia_rate = comparison['concordancia'].mean()\n", "print(f'Taxa de concordância entre métodos: {concordancia_rate:.2%}')\n", "# Top regiões por ambos os métodos\n", "top_both = comparison[\n", "    (comparison['rank_supervisionado'] <= 10) & (comparison['cluster_rank'] <= 3)\n", "].sort_values('rank_supervisionado')\n", "print('\\nRegiões priorizadas por AMBOS os métodos:')\n", "print(top_both[[GROUP_KEY, 'rank_supervisionado', 'cluster_rank', 'score_supervisionado']])\n", "# Salvar comparação\n", "comparison.to_csv(REPORTS/'comparacao_metodos.csv', index=False)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "b8cb561c", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.266973Z", "iopub.status.busy": "2025-09-11T19:52:12.266416Z", "iopub.status.idle": "2025-09-11T19:52:12.277968Z", "shell.execute_reply": "2025-09-11T19:52:12.276857Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ANÁLISE COMPARATIVA ===\n", "\n", "ABORDAGEM SUPERVISIONADA:\n", "- Foco: Predição de receita baseada em padrões históricos\n", "- Vantagem: Quantifica potencial financeiro diretamente\n", "- Limitação: Dependente da qualidade dos dados históricos\n", "- Uso recomendado: Expansão em mercados similares aos existentes\n", "\n", "ABORDAGEM NÃO-SUPERVISIONADA:\n", "- Foco: Identificação de padrões e segmentos territoriais\n", "- Vantagem: Descobre grupos não óbvios, útil para estratégias diferenciadas\n", "- Limitação: Não quantifica diretamente o potencial financeiro\n", "- Uso recomendado: Exploração de novos mercados, estratégias por segmento\n", "\n", "CONCORDÂNCIA:\n", "- 88.9% das regiões têm ranking similar entre métodos\n", "- 6 regiões são priorizadas por ambos os métodos\n"]}], "source": ["# An<PERSON>lise das diferenças metodológicas\n", "print('=== ANÁLISE COMPARATIVA ===')\n", "print('\\nABORDAGEM SUPERVISIONADA:')\n", "print('- Foco: Predição de receita baseada em padrões históricos')\n", "print('- Vantagem: Quantifica potencial financeiro diretamente')\n", "print('- Limitação: Dependente da qualidade dos dados históricos')\n", "print('- Uso recomendado: Expansão em mercados similares aos existentes')\n", "print('\\nABORDAGEM NÃO-SUPERVISIONADA:')\n", "print('- Foco: Identificação de padrões e segmentos territoriais')\n", "print('- Vantagem: Descobre grupos não óbvios, útil para estratégias diferenciadas')\n", "print('- Limitação: Não quantifica diretamente o potencial financeiro')\n", "print('- <PERSON><PERSON> recomendado: Exploração de novos mercados, estratégias por segmento')\n", "print('\\nCONCORDÂNCIA:')\n", "print(f'- {concordancia_rate:.1%} das regiões têm ranking similar entre métodos')\n", "print(f'- {len(top_both)} regiões são priorizadas por ambos os métodos')\n"]}, {"cell_type": "markdown", "id": "9b98cb94", "metadata": {}, "source": ["## 5. Dashboard Interativo e Visualizações"]}, {"cell_type": "code", "execution_count": 12, "id": "525afda8", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.282223Z", "iopub.status.busy": "2025-09-11T19:52:12.282223Z", "iopub.status.idle": "2025-09-11T19:52:12.295355Z", "shell.execute_reply": "2025-09-11T19:52:12.295355Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Folium não disponível - pulando mapeamento interativo\n"]}], "source": ["# Mapa interativo completo\n", "try:\n", "    import folium\n", "    from folium import plugins\n", "    # Mapa base\n", "    center = [-14.2350, -51.9253]\n", "    m_complete = folium.Map(location=center, zoom_start=4)\n", "    # Cores para clusters\n", "    cluster_colors = ['red', 'blue', 'green', 'purple', 'orange', 'darkred', 'lightred', 'beige']\n", "    if {'latitude','longitude'}.issubset(regional_data.columns):\n", "        # Merge dados completos\n", "        map_data = regional_data.merge(comparison, on=GROUP_KEY, how='left')\n", "        for _, row in map_data.iterrows():\n", "            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):\n", "                # <PERSON><PERSON><PERSON> baseado no score supervisionado\n", "                radius = max(5, min(15, row.get('score_supervisionado', 5)))\n", "                # Cor baseada no cluster\n", "                cluster_idx = int(row.get('cluster', 0)) % len(cluster_colors)\n", "                color = cluster_colors[cluster_idx]\n", "                # Popup informativo\n", "                popup_text = f\"\"\"\n", "                <b>{row[GROUP_KEY]}</b><br>\n", "                Score Supervisionado: {row.get('score_supervisionado', 'N/A'):.1f}<br>\n", "                Cluster: {row.get('cluster', 'N/A')}<br>\n", "                Rank Supervisionado: {row.get('rank_supervisionado', 'N/A')}<br>\n", "                Rank Cluster: {row.get('cluster_rank', 'N/A')}\n", "                \"\"\"\n", "                folium.CircleMarker(\n", "                    [row['latitude'], row['longitude']],\n", "                    radius=radius,\n", "                    color=color,\n", "                    fill=True,\n", "                    popup=folium.Popup(popup_text, max_width=300)\n", "                ).add_to(m_complete)\n", "        # Adicionar legenda\n", "        legend_html = '<div style=\"position: fixed; top: 10px; right: 10px; z-index:1000; background-color:white; padding:10px; border:2px solid grey;\">'\n", "        legend_html += '<h4>Legenda</h4>'\n", "        legend_html += '<p><b><PERSON><PERSON><PERSON>:</b> Score Supervisionado</p>'\n", "        legend_html += '<p><b>Cor:</b> Cluster Não-Supervisionado</p>'\n", "        legend_html += '</div>'\n", "        m_complete.get_root().html.add_child(folium.Element(legend_html))\n", "        m_complete.save(str(REPORTS/'mapa_interativo_completo.html'))\n", "        print('Mapa interativo completo salvo em:', REPORTS/'mapa_interativo_completo.html')\n", "    else:\n", "        print('Coordenadas não disponíveis para mapeamento completo')\n", "except ImportError:\n", "    print('Folium não disponível - pulando mapeamento interativo')\n"]}, {"cell_type": "markdown", "id": "782a5b3a", "metadata": {}, "source": ["## 6. Recomendaç<PERSON><PERSON> Executivas"]}, {"cell_type": "code", "execution_count": 13, "id": "e483f3d4", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.298294Z", "iopub.status.busy": "2025-09-11T19:52:12.298294Z", "iopub.status.idle": "2025-09-11T19:52:12.311278Z", "shell.execute_reply": "2025-09-11T19:52:12.310293Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== SÍNTESE EXECUTIVA ===\n", "\n", "Total de regiões analisadas: 9\n", "Clusters identificados: 5\n", "Taxa de concordância entre métodos: 88.9%\n", "\n", "=== TOP 5 REGIÕES INTEGRADAS ===\n", "1. Grupo_9\n", "   Score Supervisionado: 10.0 (Rank: 1)\n", "   Cluster: 3 (Rank: 1)\n", "   Score Integrado: 1.000\n", "\n", "2. Grupo_8\n", "   Score Supervisionado: 9.8 (Rank: 2)\n", "   Cluster: 1 (Rank: 3)\n", "   Score Integrado: 0.810\n", "\n", "3. Grupo_6\n", "   Score Supervisionado: 9.5 (Rank: 3)\n", "   Cluster: 1 (Rank: 3)\n", "   Score Integrado: 0.740\n", "\n", "4. Grupo_7\n", "   Score Supervisionado: 9.5 (Rank: 4)\n", "   Cluster: 2 (Rank: 2)\n", "   Score Integrado: 0.730\n", "\n", "5. Grupo_5\n", "   Score Supervisionado: 6.9 (Rank: 5)\n", "   Cluster: 1 (Rank: 3)\n", "   Score Integrado: 0.600\n", "\n"]}], "source": ["# Síntese dos resultados\n", "print('=== SÍNTESE EXECUTIVA ===')\n", "print(f'\\nTotal de regiões analisadas: {len(regional_data)}')\n", "print(f'Clusters identificados: {best_k}')\n", "print(f'Taxa de concordância entre métodos: {concordancia_rate:.1%}')\n", "# Top 5 integrado (priorizando concordância)\n", "top5_integrated = comparison.copy()\n", "# Score integrado: média ponderada (70% supervisionado, 30% cluster)\n", "top5_integrated['score_integrado'] = (\n", "    0.7 * (11 - top5_integrated['rank_supervisionado']) / 10 +  # Normalizar rank\n", "    0.3 * (best_k + 1 - top5_integrated['cluster_rank']) / best_k\n", ")\n", "top5_integrated = top5_integrated.sort_values('score_integrado', ascending=False)\n", "print('\\n=== TOP 5 REGIÕES INTEGRADAS ===')\n", "for i, (_, row) in enumerate(top5_integrated.head(5).iterrows(), 1):\n", "    print(f'{i}. {row[GROUP_KEY]}')\n", "    print(f'   Score Supervisionado: {row[\"score_supervisionado\"]:.1f} (Rank: {row[\"rank_supervisionado\"]})')\n", "    print(f'   Cluster: {row[\"cluster\"]} (Rank: {row[\"cluster_rank\"]})')\n", "    print(f'   Score Integrado: {row[\"score_integrado\"]:.3f}')\n", "    print()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "d601b18c", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.313820Z", "iopub.status.busy": "2025-09-11T19:52:12.312285Z", "iopub.status.idle": "2025-09-11T19:52:12.328510Z", "shell.execute_reply": "2025-09-11T19:52:12.327683Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Recomendações executivas salvas em: ..\\reports\\2025-08-15\\territorial_analysis\\recomendacoes_executivas.csv\n"]}], "source": ["# Recomendações específicas por método\n", "recommendations = []\n", "# Top 5 supervisionado\n", "for i, (_, row) in enumerate(ranking_sup.head(5).iterrows(), 1):\n", "    recommendations.append({\n", "        'rank': i,\n", "        'regiao': row[GROUP_KEY],\n", "        'metodo': 'Supervisionado',\n", "        'score': row['score_supervisionado'],\n", "        'justificativa': f'Alto potencial de receita predito (R$ {row[\"receita_pred\"]:,.0f})',\n", "        'acao_recomendada': 'Expansão prioritária - ROI esperado alto'\n", "    })\n", "# Top clusters\n", "cluster_recs = cluster_analysis.groupby('cluster').first().sort_values('cluster_rank')\n", "for i, (cluster, row) in enumerate(cluster_recs.head(3).iterrows(), 1):\n", "    recommendations.append({\n", "        'rank': i,\n", "        'regiao': f'Cluster {cluster} (ex: {row[GROUP_KEY]})',\n", "        'metodo': 'Não-Supervisionado',\n", "        'score': f'Cluster {cluster}',\n", "        'justificativa': f'Segmento territorial de alto potencial',\n", "        'acao_recomendada': f'Estratégia diferenciada para cluster {cluster}'\n", "    })\n", "# Salvar recomendações\n", "rec_df = pd.DataFrame(recommendations)\n", "rec_df.to_csv(REPORTS/'recomendacoes_executivas.csv', index=False)\n", "print('Recomendações executivas salvas em:', REPORTS/'recomendacoes_executivas.csv')\n"]}, {"cell_type": "markdown", "id": "fed738be", "metadata": {}, "source": ["### 6.1 Persistência dos Modelos"]}, {"cell_type": "code", "execution_count": 15, "id": "a71f0512", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.330568Z", "iopub.status.busy": "2025-09-11T19:52:12.330568Z", "iopub.status.idle": "2025-09-11T19:52:12.436859Z", "shell.execute_reply": "2025-09-11T19:52:12.435875Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modelos salvos em:\n", "- Supervisionado: ..\\models\\territorial_supervised.pkl\n", "- Não-supervisionado: ..\\models\\territorial_unsupervised.pkl\n"]}], "source": ["# Salvar modelos treinados\n", "supervised_model = {\n", "    'model': rf_model,\n", "    'scaler': scaler_sup,\n", "    'score_scaler': score_scaler,\n", "    'features': X_features.columns.tolist(),\n", "    'metrics': metrics_sup,\n", "    'group_key': GROUP_KEY\n", "}\n", "unsupervised_model = {\n", "    'model': kmeans_final,\n", "    'scaler': scaler_unsup,\n", "    'features': X_features.columns.tolist(),\n", "    'best_k': best_k,\n", "    'silhouette_score': best_silhouette,\n", "    'group_key': GROUP_KEY\n", "}\n", "joblib.dump(supervised_model, MODELS/'territorial_supervised.pkl')\n", "joblib.dump(unsupervised_model, MODELS/'territorial_unsupervised.pkl')\n", "print('Modelos salvos em:')\n", "print('- Supervisionado:', MODELS/'territorial_supervised.pkl')\n", "print('- <PERSON><PERSON>-supervisionado:', MODELS/'territorial_unsupervised.pkl')\n"]}, {"cell_type": "markdown", "id": "9fce2fb5", "metadata": {}, "source": ["## 7. Resumo Final e Próximos Passos"]}, {"cell_type": "code", "execution_count": 16, "id": "bfbc5a3d", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.440673Z", "iopub.status.busy": "2025-09-11T19:52:12.439690Z", "iopub.status.idle": "2025-09-11T19:52:12.450515Z", "shell.execute_reply": "2025-09-11T19:52:12.449888Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== RESUMO FINAL ===\n", "\n", "✅ ENTREGÁVEIS GERADOS:\n", "- Ranking supervisionado: ..\\reports\\2025-08-15\\territorial_analysis\\ranking_supervisionado.csv\n", "- Clusters não-supervisionados: ..\\reports\\2025-08-15\\territorial_analysis\\clusters_nao_supervisionado.csv\n", "- Comparação de métodos: ..\\reports\\2025-08-15\\territorial_analysis\\comparacao_metodos.csv\n", "- Mapa interativo: ..\\reports\\2025-08-15\\territorial_analysis\\mapa_interativo_completo.html\n", "- Recomendações executivas: ..\\reports\\2025-08-15\\territorial_analysis\\recomendacoes_executivas.csv\n", "- Modelos treinados em models/\n", "\n", "🎯 PRÓXIMOS PASSOS:\n", "1. Validar recomendações com equipe comercial\n", "2. <PERSON><PERSON> dados externos (demografia, concorrência) para enriquecer análise\n", "3. Implementar monitoramento de performance das regiões priorizadas\n", "4. Desenvolver dashboard executivo para acompanhamento contínuo\n", "\n", "📊 CRITÉRIOS DE ESCOLHA DO MÉTODO:\n", "- Use SUPERVISIONADO quando: foco em ROI, mercados similares aos existentes\n", "- Use NÃO-SUPERVISIONADO quando: exploração de novos mercados, estratégias diferenciadas\n", "- Use AMBOS quando: decisões críticas que requerem múltiplas perspectivas\n"]}], "source": ["print('=== RESUMO FINAL ===')\n", "print('\\n✅ ENTREGÁVEIS GERADOS:')\n", "print('- Ranking supervisionado:', REPORTS/'ranking_supervisionado.csv')\n", "print('- Clusters não-supervisionados:', REPORTS/'clusters_nao_supervisionado.csv')\n", "print('- Comparação de métodos:', REPORTS/'comparacao_metodos.csv')\n", "print('- Mapa interativo:', REPORTS/'mapa_interativo_completo.html')\n", "print('- Recomendações executivas:', REPORTS/'recomendacoes_executivas.csv')\n", "print('- Modelos treinados em models/')\n", "print('\\n🎯 PRÓXIMOS PASSOS:')\n", "print('1. Validar recomendações com equipe comercial')\n", "print('2. Coletar dados externos (demografia, concorrência) para enriquecer análise')\n", "print('3. Implementar monitoramento de performance das regiões priorizadas')\n", "print('4. Desenvolver dashboard executivo para acompanhamento contínuo')\n", "print('\\n📊 CRITÉRIOS DE ESCOLHA DO MÉTODO:')\n", "print('- Use SUPERVISIONADO quando: foco em ROI, mercados similares aos existentes')\n", "print('- Use NÃO-SUPERVISIONADO quando: exploração de novos mercados, estratégias diferenciadas')\n", "print('- Use AMBOS quando: decisões críticas que requerem múltiplas perspectivas')\n"]}, {"cell_type": "markdown", "id": "806c6c61", "metadata": {}, "source": ["## Visualizações de Clusterização Territorial (não supervisionado) e Performance Supervisionada por Cluster\n", "\n", "Integra os resultados de segmentação (Notebook 05) com a modelagem supervisionada (Notebook 03) para:\n", "1) visualizar como o algoritmo não supervisionado particiona o território; e 2) avaliar o desempenho do melhor modelo por cluster.\n"]}, {"cell_type": "code", "execution_count": 17, "id": "8ecc4e49", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T19:52:12.454428Z", "iopub.status.busy": "2025-09-11T19:52:12.454428Z", "iopub.status.idle": "2025-09-11T19:53:56.267094Z", "shell.execute_reply": "2025-09-11T19:53:56.266110Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded data: ..\\data\\processed\\features_engineered_regional.csv (35616, 186)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Built proxy clusters (k=5) from territorial signals\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Best model from ranking: MLP\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Two-stage territorial visualization with robust fallbacks\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.svm import SVR\n", "import warnings; warnings.filterwarnings('ignore')\n", "\n", "BASE = Path('.')\n", "if not (BASE/'data'/'processed'/'features_engineered_regional.csv').exists(): BASE = Path('..')\n", "DATA = BASE/'data'/'processed'/'features_engineered_regional.csv' if (BASE/'data'/'processed'/'features_engineered_regional.csv').exists() else BASE/'data'/'processed'/'features_engineered.csv'\n", "REPORTS = BASE/'reports'/'2025-08-15'\n", "PLOTS = REPORTS/'plots'\n", "TABLES = REPORTS/'tables'\n", "PLOTS.mkdir(parents=True, exist_ok=True); TABLES.mkdir(parents=True, exist_ok=True)\n", "\n", "# Load main data\n", "df = pd.read_csv(DATA, low_memory=False)\n", "print('Loaded data:', DATA, df.shape)\n", "\n", "# 1) Load clustering labels from notebook 05 if available\n", "clust_assign_path = TABLES/'cluster_assignments.csv'\n", "cluster_labels = None\n", "if clust_assign_path.exists():\n", "    try:\n", "        cl = pd.read_csv(clust_assign_path)\n", "        if 'index' in cl.columns:\n", "            cl = cl.set_index('index').sort_index()\n", "        if 'cluster' in cl.columns:\n", "            if len(cl) >= len(df):\n", "                cluster_labels = cl['cluster'].iloc[:len(df)].values\n", "            else:\n", "                cluster_labels = cl['cluster'].reindex(range(len(df))).values\n", "        print('Loaded clustering assignments from', clust_assign_path)\n", "    except Exception as e:\n", "        print('WARN: failed to load cluster assignments ->', e)\n", "\n", "# Fallback: build proxy clusters from territorial features if needed\n", "if cluster_labels is None:\n", "    try:\n", "        territorial_cols = []\n", "        if 'UF' in df.columns: territorial_cols.append('UF')\n", "        territorial_cols += [c for c in df.columns if 'REGIAO_CHILLI' in c]\n", "        X_terr = df[territorial_cols].copy() if territorial_cols else pd.DataFrame(index=df.index)\n", "        transformers=[]\n", "        if 'UF' in X_terr.columns:\n", "            transformers.append(('uf_ohe', OneHotEncoder(handle_unknown='ignore'), ['UF']))\n", "        fixed = [c for c in X_terr.columns if c!='UF']\n", "        if fixed:\n", "            transformers.append(('pass', 'passthrough', fixed))\n", "        if not transformers:\n", "            num_cols=[c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n", "            X_terr = df[num_cols[:8]].copy(); transformers=[('sc','passthrough', X_terr.columns.tolist())]\n", "        pre = ColumnTransformer(transformers)\n", "        X_enc = pre.fit_transform(X_terr)\n", "        from sklearn.cluster import KMeans\n", "        k = 5\n", "        km = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "        cluster_labels = km.fit_predict(X_enc)\n", "        print('Built proxy clusters (k=5) from territorial signals')\n", "    except Exception as e:\n", "        print('ERROR: unable to construct proxy clusters ->', e)\n", "        cluster_labels = np.zeros(len(df), dtype=int)\n", "\n", "# 1) Territorial clustering visualization\n", "try:\n", "    map_path = PLOTS/'territorial_clustering_map.png'\n", "    if 'UF' in df.columns:\n", "        order = sorted(df['UF'].dropna().unique())\n", "        uf_cat = pd.Categorical(df['UF'], categories=order, ordered=True)\n", "        plt.figure(figsize=(12,6))\n", "        y = np.random.RandomState(42).rand(len(df))\n", "        sns.scatterplot(x=uf_cat, y=y, hue=cluster_labels, palette='tab10', s=6, alpha=0.5, legend=False)\n", "        sizes = pd.Series(cluster_labels).value_counts().sort_index()\n", "        handles = [plt.Line2D([0],[0], marker='o', color='w', label=f'Cluster {i} (n={sizes.get(i,0)})', markerfacecolor=plt.cm.tab10(i%10), markersize=8) for i in range(int(pd.Series(cluster_labels).max())+1)]\n", "        plt.legend(handles=handles, title='Clusters', bbox_to_anchor=(1.02,1), loc='upper left')\n", "        plt.title('Segmentação Territorial por UF (cores=clusters)')\n", "        plt.xlabel('UF'); plt.ylabel('pos. relativa (visual)'); plt.tight_layout();\n", "        plt.savefig(map_path, dpi=180, bbox_inches='tight'); plt.show()\n", "    else:\n", "        region_oh = [c for c in df.columns if 'REGIAO_CHILLI' in c]\n", "        if region_oh:\n", "            vals = pd.DataFrame(df[region_oh].values, columns=region_oh)\n", "            idx = np.argmax(vals.values, axis=1)\n", "            cats = [region_oh[i] for i in idx]\n", "            cat_series = pd.Series(cats, index=df.index, name='territory')\n", "            order = sorted(list(pd.unique(cat_series)))\n", "            y = np.random.RandomState(42).rand(len(df))\n", "            plt.figure(figsize=(12,6))\n", "            sns.scatterplot(x=pd.Categorical(cat_series, categories=order, ordered=True), y=y, hue=cluster_labels, palette='tab10', s=6, alpha=0.5, legend=False)\n", "            sizes = pd.Series(cluster_labels).value_counts().sort_index()\n", "            handles = [plt.Line2D([0],[0], marker='o', color='w', label=f'Cluster {i} (n={sizes.get(i,0)})', markerfacecolor=plt.cm.tab10(i%10), markersize=8) for i in range(int(pd.Series(cluster_labels).max())+1)]\n", "            plt.legend(handles=handles, title='Clusters', bbox_to_anchor=(1.02,1), loc='upper left')\n", "            plt.title('Segmentação Territorial por Região (REGIAO_CHILLI) — clusters')\n", "            plt.xlabel('<PERSON>i<PERSON>'); plt.ylabel('pos. relativa (visual)'); plt.tight_layout(); plt.savefig(map_path, dpi=180, bbox_inches='tight'); plt.show()\n", "        else:\n", "            sizes = pd.Series(cluster_labels).value_counts().sort_index()\n", "            plt.figure(figsize=(8,4)); sns.barplot(x=sizes.index, y=sizes.values)\n", "            plt.title('<PERSON><PERSON><PERSON> dos clusters (fallback)'); plt.xlabel('Cluster'); plt.ylabel('n'); plt.tight_layout(); plt.savefig(map_path, dpi=180); plt.show()\n", "except Exception as e:\n", "    print('WARN: failed to build territorial clustering map ->', e)\n", "\n", "# 2) Supervised model performance by cluster\n", "rank_path = TABLES/'algorithm_ranking.csv'\n", "best_model_name = None\n", "if rank_path.exists():\n", "    try:\n", "        rank = pd.read_csv(rank_path)\n", "        if 'rmse_mean' in rank.columns:\n", "            best_model_name = rank.sort_values('rmse_mean').iloc[0]['model']\n", "        elif 'r2_mean' in rank.columns:\n", "            best_model_name = rank.sort_values('r2_mean', ascending=False).iloc[0]['model']\n", "        print('Best model from ranking:', best_model_name)\n", "    except Exception as e:\n", "        print('WARN: could not read ranking ->', e)\n", "if best_model_name is None:\n", "    best_model_name = 'RF'\n", "\n", "num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n", "candidates = [c for c in ['valor','sq_valor','y','sales','revenue'] if c in df.columns]\n", "target = candidates[0] if candidates else (num_cols[0] if num_cols else None)\n", "if target in num_cols:\n", "    num_cols = [c for c in num_cols if c!=target]\n", "\n", "from typing import List\n", "\n", "def make_model(name:str):\n", "    if name in ['RF','GB','RandomForest']:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    elif name in ['SVM','SVR']:\n", "        base = SVR(kernel='rbf')\n", "    elif name in ['LR','Linear','LinearRegression']:\n", "        base = LinearRegression()\n", "    else:\n", "        base = RandomForestRegressor(n_estimators=300, random_state=42)\n", "    pre = ColumnTransformer([\n", "        ('num', StandardScaler(with_mean=False), num_cols)\n", "    ], remainder='drop')\n", "    return Pipeline([('pre', pre), ('model', base)])\n", "\n", "metrics_rows = []\n", "all_preds = []\n", "if target is None or not num_cols:\n", "    print('ERROR: target or numeric features not available; skipping supervised by cluster')\n", "else:\n", "    uniq = sorted(pd.Series(cluster_labels).dropna().unique())\n", "    for c_id in uniq:\n", "        mask = (pd.Series(cluster_labels)==c_id).values\n", "        dfc = df.loc[mask]\n", "        if len(dfc) < 100:\n", "            print(f'Cluster {int(c_id)}: too few samples ({len(dfc)}), skipping metrics')\n", "            continue\n", "        X = dfc[num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n", "        Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)\n", "        pipe = make_model(str(best_model_name))\n", "        try:\n", "            pipe.fit(Xtr, ytr)\n", "            yp = pipe.predict(Xte)\n", "            r2 = float(r2_score(yte, yp))\n", "            rmse = float(np.sqrt(mean_squared_error(yte, yp)))\n", "            mae = float(mean_absolute_error(yte, yp))\n", "            metrics_rows.append({'cluster':int(c_id),'n':int(len(dfc)),'r2':r2,'rmse':rmse,'mae':mae})\n", "            all_preds.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))\n", "        except Exception as e:\n", "            print(f'Cluster {int(c_id)}: training failed ->', e)\n", "\n", "    if metrics_rows:\n", "        mdf = pd.DataFrame(metrics_rows).sort_values('cluster')\n", "        mdf.to_csv(TABLES/'supervised_by_cluster_metrics.csv', index=False)\n", "        fig, axes = plt.subplots(1,3, figsize=(14,4))\n", "        sns.barplot(data=mdf, x='cluster', y='r2', ax=axes[0]); axes[0].set_title('R² por Cluster');\n", "        sns.barplot(data=mdf, x='cluster', y='rmse', ax=axes[1]); axes[1].set_title('RMSE por Cluster');\n", "        sns.barplot(data=mdf, x='cluster', y='mae', ax=axes[2]); axes[2].set_title('MAE por Cluster');\n", "        plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_performance.png', dpi=180); plt.show()\n", "\n", "    if all_preds:\n", "        preds = pd.concat(all_preds, ignore_index=True)\n", "        preds.to_csv(TABLES/'supervised_by_cluster_predictions.csv', index=False)\n", "        plt.figure(figsize=(6,6));\n", "        sns.scatterplot(data=preds, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)\n", "        lim = (min(preds['y_true'].min(), preds['y_pred'].min()), max(preds['y_true'].max(), preds['y_pred'].max()))\n", "        plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_predictions.png', dpi=180); plt.show()\n", "\n", "# Feature importances per cluster (if supported)\n", "try:\n", "    if str(best_model_name) in ['RF','GB','RandomForest'] and num_cols:\n", "        for c_id in sorted(pd.Series(cluster_labels).dropna().unique()):\n", "            mask = (pd.Series(cluster_labels)==c_id).values\n", "            dfc = df.loc[mask]\n", "            if len(dfc) < 200: continue\n", "            X = dfc[num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n", "            pipe = make_model('RF'); pipe.fit(X, y)\n", "            model = pipe.named_steps['model']\n", "            if hasattr(model, 'feature_importances_'):\n", "                imps = model.feature_importances_\n", "                top_idx = np.argsort(imps)[-10:][::-1]\n", "                out = pd.DataFrame({'feature':[num_cols[i] for i in top_idx], 'importance':[float(imps[i]) for i in top_idx]})\n", "                out['cluster']=int(c_id)\n", "                out.to_csv(TABLES/f'feature_importances_top10_cluster_{int(c_id)}.csv', index=False)\n", "except Exception as e:\n", "    print('Feature importance by cluster skipped ->', e)\n"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}