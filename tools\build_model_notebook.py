import nbformat as nbf
from pathlib import Path

md = lambda t: nbf.v4.new_markdown_cell(t)
code = lambda t: nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / 'model_comparison.ipynb'

nb = nbf.v4.new_notebook()

nb.cells.append(md('# Model Comparison - Chilli Beans\n\nComparação de algoritmos com validação cruzada e métricas padronizadas.'))

# Setup
nb.cells.append(md('## 1. Setup'))
nb.cells.append(code(
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np\n"
    "import matplotlib.pyplot as plt, seaborn as sns\n"
    "from sklearn.model_selection import StratifiedKFold, KFold, cross_validate\n"
    "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, average_precision_score, confusion_matrix, roc_curve, precision_recall_curve, mean_squared_error, r2_score, mean_absolute_error\n"
    "from sklearn.preprocessing import StandardScaler\n"
    "from sklearn.pipeline import Pipeline\n"
    "from sklearn.linear_model import LogisticRegression, LinearRegression\n"
    "from sklearn.svm import SVC, SVR\n"
    "from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor\n"
    "from sklearn.neural_network import MLPClassifier, MLPRegressor\n"
    "import warnings; warnings.filterwarnings('ignore')\n"
    "BASE = Path('.')\n"
    "if not (BASE / 'data' / 'processed' / 'features_engineered.csv').exists(): BASE = Path('..')\n"
    "DATA = BASE / 'data' / 'processed' / 'features_engineered.csv'\n"
    "REPORTS = BASE / 'reports' / '2025-08-15'\n"
    "PLOTS = REPORTS / 'plots' / 'model_comparison'\n"
    "TABLES = REPORTS / 'tables'\n"
    "for d in [PLOTS, TABLES]: d.mkdir(parents=True, exist_ok=True)\n"
))

# Load data and infer task type
nb.cells.append(md('## 2. Carregamento de Dados e Tipo de Problema'))
nb.cells.append(code(
    "df = pd.read_csv(DATA)\n"
    "# Downsample para execução estável no ambiente local\n"
    "MAX_N = 5000\n"
    "if len(df) > MAX_N:\n"
    "    df = df.sample(MAX_N, random_state=42).reset_index(drop=True)\n"
    "# Heurística para TARGET: preferir 'valor' se existir; senão colunas típicas; por fim a última\n"
    "if 'valor' in df.columns:\n"
    "    TARGET = 'valor'\n"
    "else:\n"
    "    possible_targets = [c for c in df.columns if c.lower() in ('target','label','y','classe','class')]\n"
    "    TARGET = possible_targets[0] if possible_targets else df.columns[-1]\n"
    "y = df[TARGET]\n"
    "X = df.drop(columns=[TARGET])\n"
    "# Usar apenas variáveis numéricas para evitar overhead de codificação durante comparações\n"
    "X = X.select_dtypes(include=['number'])\n"
    "# Classificação se y é inteiro com poucas classes; caso contrário regressão\n"
    "is_classification = (pd.api.types.is_integer_dtype(y) and y.nunique()<=10) or (y.dtype=='object')\n"
    "print('TARGET:', TARGET, '| task:', 'classification' if is_classification else 'regression', '| X:', X.shape)\n"
))

# Define models
nb.cells.append(md('## 3. Modelos e Validação Cruzada'))
nb.cells.append(code(
    "np.random.seed(42)\n"
    "if is_classification:\n"
    "    models = {\n"
    "        'LogReg': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', LogisticRegression(max_iter=200))]),\n"
    "        'RF': RandomForestClassifier(n_estimators=200, random_state=42),\n"
    "        'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', SVC(kernel='rbf', probability=True, random_state=42))]),\n"
    "        'GB': RandomForestClassifier(n_estimators=400, max_depth=None, random_state=42),\n"
    "        'MLP': MLPClassifier(hidden_layer_sizes=(64,32), max_iter=300, random_state=42)\n"
    "    }\n"
    "    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n"
    "    scoring = ['accuracy','precision_weighted','recall_weighted','f1_weighted','roc_auc_ovr']\n"
    "else:\n"
    "    models = {\n"
    "        'LinReg': LinearRegression(),\n"
    "        'RF': RandomForestRegressor(n_estimators=300, random_state=42),\n"
    "        'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('svr', SVR(kernel='rbf'))]),\n"
    "        'GB': RandomForestRegressor(n_estimators=600, max_depth=None, random_state=42),\n"
    "        'MLP': MLPRegressor(hidden_layer_sizes=(64,32), max_iter=400, random_state=42)\n"
    "    }\n"
    "    cv = KFold(n_splits=5, shuffle=True, random_state=42)\n"
    "    scoring = {'rmse': 'neg_root_mean_squared_error', 'mae': 'neg_mean_absolute_error', 'r2': 'r2'}\n"
))

# Cross-validate
nb.cells.append(md('## 4. Validação Cruzada e Métricas'))
nb.cells.append(code(
    "results = []\n"
    "for name, model in models.items():\n"
    "    cvres = cross_validate(model, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n"
    "    res = { 'model': name }\n"
    "    for k,v in cvres.items():\n"
    "        if k.startswith('test_'):\n"
    "            mname = k.replace('test_','')\n"
    "            res[mname+'_mean'] = float(np.mean(v))\n"
    "            res[mname+'_std'] = float(np.std(v))\n"
    "    results.append(res)\n"
    "res_df = pd.DataFrame(results)\n"
    "res_df.to_csv(TABLES / 'algorithm_ranking.csv', index=False)\n"
    "res_df\n"
))

# Visualizations
nb.cells.append(md('## 5. Visualizações de Performance'))
nb.cells.append(code(
    "PLOTS.mkdir(parents=True, exist_ok=True)\n"
    "if is_classification:\n"
    "    # Boxplot F1-weighted como exemplo\n"
    "    # (cross_validate não retorna valores fold-by-fold diretamente por métrica nomeada, simplificamos com o df de summary)\n"
    "    plt.figure(figsize=(6,4))\n"
    "    sns.barplot(data=res_df, x='model', y='f1_weighted_mean')\n"
    "    plt.title('F1 (weighted) médio por modelo'); plt.tight_layout(); plt.savefig(PLOTS / 'bar_f1.png'); plt.show()\n"
    "else:\n"
    "    plt.figure(figsize=(6,4))\n"
    "    sns.barplot(data=res_df, x='model', y='rmse_mean')\n"
    "    plt.title('RMSE médio por modelo'); plt.tight_layout(); plt.savefig(PLOTS / 'bar_rmse.png'); plt.show()\n"
))

# Recommendations
nb.cells.append(md('## 6. Recomendações'))
nb.cells.append(code(
    "import numpy as np\n"
    "recs = []\n"
    "if is_classification:\n"
    "    # Melhor por F1\n"
    "    best = res_df.sort_values('f1_weighted_mean', ascending=False).iloc[0]\n"
    "    recs.append({'scenario':'equilíbrio precisão/recall','recommended': best['model'],'metric': 'f1_weighted_mean', 'value': float(best['f1_weighted_mean'])})\n"
    "else:\n"
    "    best = res_df.sort_values('rmse_mean', ascending=True).iloc[0]\n"
    "    recs.append({'scenario':'menor erro','recommended': best['model'], 'metric': 'rmse_mean', 'value': float(best['rmse_mean'])})\n"
    "rec_df = pd.DataFrame(recs)\n"
    "rec_df.to_csv(TABLES/'model_recommendations.csv', index=False)\n"
    "rec_df\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Model comparison notebook built at', NB_PATH)

