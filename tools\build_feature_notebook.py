import nbformat as nbf
from pathlib import Path

# Helpers
md = lambda t: nbf.v4.new_markdown_cell(t)
code = lambda t: nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / 'feature_engineering.ipynb'

nb = nbf.v4.new_notebook()

# 0. Header
nb.cells.append(md(
    "# Feature Engineering - Chilli Beans Dataset\n\n"
    "Notebook dedicado ao preparo de variáveis para modelagem, fundamentado nos resultados da Parte 1 (EDA)."
))

# 1. Setup e Importação
nb.cells.append(md("## 1. Setup e Importação de Resultados da EDA"))
nb.cells.append(code(
    "# Paths e imports\n"
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np, json, math\n"
    "import matplotlib.pyplot as plt, seaborn as sns\n"
    "from warnings import filterwarnings; filterwarnings('ignore')\n"
    "BASE_DIR = Path('.')\n"
    "if not (BASE_DIR / 'data' / 'clean' / 'cleaned_featured.csv').exists(): BASE_DIR = Path('..')\n"
    "DATA_CLEAN = BASE_DIR / 'data' / 'clean' / 'cleaned_featured.csv'\n"
    "PROC_DIR = BASE_DIR / 'data' / 'processed'\n"
    "REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'\n"
    "TABLES_DIR = REPORTS_DIR / 'tables'\n"
    "PLOTS_FE_DIR = REPORTS_DIR / 'plots' / 'feature_engineering'\n"
    "for d in [PROC_DIR, TABLES_DIR, PLOTS_FE_DIR]: d.mkdir(parents=True, exist_ok=True)\n"
    "CFG_JSON = TABLES_DIR / 'preprocessing_config.json'\n"
    "NORM_CSV = TABLES_DIR / 'normality_tests.csv'\n"
    "REC_CSV = TABLES_DIR / 'outlier_method_recommendations.csv'\n"
    "print('DATA_CLEAN:', DATA_CLEAN)\n"
))
nb.cells.append(code(
    "# Carregar dados e artefatos da EDA\n"
    "df = pd.read_csv(DATA_CLEAN, parse_dates=['data'])\n"
    "cfg = json.loads(CFG_JSON.read_text(encoding='utf-8')) if CFG_JSON.exists() else {}\n"
    "norm_tbl = pd.read_csv(NORM_CSV) if NORM_CSV.exists() else pd.DataFrame()\n"
    "rec_tbl = pd.read_csv(REC_CSV) if REC_CSV.exists() else pd.DataFrame()\n"
    "print('cfg vars:', len(cfg), '| norm_tbl:', norm_tbl.shape, '| rec_tbl:', rec_tbl.shape)\n"
))

# 2. Seleção Fundamentada de Variáveis
nb.cells.append(md("## 2. Seleção Fundamentada de Variáveis"))
nb.cells.append(code(
    "# Heurísticas: remover constantes/baixa variância; multicolinearidade alta;\n"
    "num_cols = df.select_dtypes(include=['number']).columns.tolist()\n"
    "# Remover IDs explícitos da etapa de modelagem (mantê-los apenas para joins/contagens)\n"
    "id_like = [c for c in num_cols if str(c).lower().replace(' ','') in ['id','id_loja','id_produto','id_cliente','id_vendedor','id_faturamento','documento','transacao'] or str(c).lower().startswith('id_') or str(c).lower().endswith('_id')]\n"
    "num_cols = [c for c in num_cols if c not in id_like]\n"
    "cat_cols = [c for c in df.select_dtypes(exclude=['number']).columns if c not in ['data']]\n"
    "# 2.1 Remover constantes e quase-constantes\n"
    "low_var = [c for c in num_cols if df[c].nunique()<=1]\n"
    "num_cols = [c for c in num_cols if c not in low_var]\n"
    "# 2.2 Correlação alta (|r|>0.95) => eliminar uma das variáveis\n"
    "corr = df[num_cols].corr(method='spearman').abs() if len(num_cols)>1 else pd.DataFrame()\n"
    "to_drop = set()\n"
    "if not corr.empty:\n"
    "    upper = corr.where(np.triu(np.ones(corr.shape), k=1).astype(bool))\n"
    "    for c in upper.columns:\n"
    "        if any(upper[c] > 0.95): to_drop.add(c)\n"
    "num_sel = [c for c in num_cols if c not in to_drop]\n"
    "# 2.3 Documentar exclusões\n"
    "exclusions = pd.concat([\n"
    "    pd.DataFrame({'reason': 'low_variance', 'col': low_var}),\n"
    "    pd.DataFrame({'reason': 'high_correlation', 'col': list(to_drop)})\n"
    "], ignore_index=True)\n"
    "display({'n_num_orig': len(df.select_dtypes(include=['number']).columns), 'n_id_like': len(id_like), 'n_num_sel': len(num_sel), 'exclusions': exclusions})\n"
))
nb.cells.append(code(
    "# Seleção final (numéricas + categóricas úteis)\n"
    "use_cols = num_sel + cat_cols + (['data'] if 'data' in df.columns else [])\n"
    "df_sel = df[use_cols].copy()\n"
    "df_sel.head()\n"
))

# 3. Transformações de Distribuição
nb.cells.append(md("## 3. Transformações de Distribuição"))
nb.cells.append(code(
    "# Regras: log1p para assimetria positiva; Box-Cox quando >0 e melhora normalidade\n"
    "from scipy.stats import boxcox\n"
    "transforms = {}\n"
    "for c in num_sel:\n"
    "    s = pd.to_numeric(df_sel[c], errors='coerce')\n"
    "    skew = s.skew()\n"
    "    applied = None\n"
    "    if skew>1 and (s>=0).all():\n"
    "        df_sel[c+'_log1p'] = np.log1p(s)\n"
    "        applied = 'log1p'\n"
    "    if applied is None and (s>0).all() and s.notna().sum()>20:\n"
    "        try:\n"
    "            bc, lam = boxcox(s.dropna())\n"
    "            df_sel[c+'_boxcox'] = np.nan; df_sel.loc[s.notna(), c+'_boxcox'] = bc\n"
    "            transforms[c] = {'distribution': applied or 'boxcox', 'lambda': float(lam)}\n"
    "            continue\n"
    "        except Exception: pass\n"
    "    if applied: transforms[c] = {'distribution': applied}\n"
    "print('Transf. distribuição aplicadas:', len(transforms))\n"
))

# 4. Tratamento de Outliers com config JSON
nb.cells.append(md("## 4. Tratamento de Outliers (baseado em config JSON)"))
nb.cells.append(code(
    "# Aplicar clipping conforme preprocessing_config.json (sigma/iqr)\n"
    "outlier_info = {}\n"
    "for c, meta in cfg.items():\n"
    "    if c in df_sel.columns and isinstance(meta.get('threshold',{}), dict):\n"
    "        th = meta['threshold']; lo, hi = th.get('low'), th.get('high')\n"
    "        before_n = df_sel[c].notna().sum()\n"
    "        df_sel[c+'_clipped'] = df_sel[c].clip(lower=lo, upper=hi)\n"
    "        outlier_info[c] = {'method': meta.get('method'), 'low': lo, 'high': hi, 'n': int(before_n)}\n"
    "print('Outliers tratados:', len(outlier_info))\n"
))

# 5. Tratamento de Valores Faltantes
nb.cells.append(md("## 5. Tratamento de Valores Faltantes"))
nb.cells.append(code(
    "from sklearn.impute import KNNImputer\n"
    "missing = df_sel.isna().mean().sort_values(ascending=False)\n"
    "low_missing_cols = [c for c in df_sel.columns if df_sel[c].isna().mean()<=0.05]\n"
    "for c in low_missing_cols:\n"
    "    if df_sel[c].dtype.kind in 'ifc': df_sel[c] = df_sel[c].fillna(df_sel[c].median())\n"
    "    else: df_sel[c] = df_sel[c].fillna(df_sel[c].mode().iloc[0])\n"
    "# KNN em numéricas restantes com missing\n"
    "num_for_impute = [c for c in df_sel.select_dtypes(include=['number']).columns if df_sel[c].isna().any()]\n"
    "if num_for_impute:\n"
    "    imputer = KNNImputer(n_neighbors=5)\n"
    "    df_sel[num_for_impute] = imputer.fit_transform(df_sel[num_for_impute])\n"
    "# Flags de missing relevantes\n"
    "for c in df.columns:\n"
    "    if df[c].isna().any(): df_sel[c+'_was_missing'] = df[c].isna().astype(int)\n"
    "missing.head()\n"
))

# 6. Codificação de Variáveis Categóricas
nb.cells.append(md("## 6. Codificação de Variáveis Categóricas"))
nb.cells.append(code(
    "# One-hot para baixa cardinalidade; Target encoding opcional (SUPERVISED)\n"
    "SUPERVISED = True; TARGET_COL = 'valor'  # Ajuste para cenário supervisionado\n"
    "from sklearn.preprocessing import OneHotEncoder\n"
    "cat_cols_use = [c for c in cat_cols if df_sel[c].nunique()<=20] if cat_cols else []\n"
    "try:\n"
    "    ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=False) if cat_cols_use else None\n"
    "except TypeError:\n"
    "    ohe = OneHotEncoder(handle_unknown='ignore', sparse=False) if cat_cols_use else None\n"
    "X_cat = ohe.fit_transform(df_sel[cat_cols_use]) if cat_cols_use else np.empty((len(df_sel),0))\n"
    "ohe_cols = (ohe.get_feature_names_out(cat_cols_use).tolist() if cat_cols_use else [])\n"
    "df_ohe = pd.DataFrame(X_cat, columns=ohe_cols, index=df_sel.index) if cat_cols_use else pd.DataFrame(index=df_sel.index)\n"
    "# Excluir IDs (e colunas *_clipped originais de ID) da matriz numérica\n"
    "id_like_all = set(id_like) | {c for c in df_sel.columns if str(c).lower().startswith('id_') or str(c).lower().endswith('_id') or str(c).lower().replace(' ','') in ['id','id_loja','id_produto','id_cliente','id_vendedor','id_faturamento','documento','transacao']}\n"
    "df_num = df_sel.select_dtypes(include=['number']).drop(columns=[c for c in df_sel.columns if c in id_like_all or c.endswith('_clipped')], errors='ignore').copy()\n"
    "df_fe = pd.concat([df_num, df_ohe], axis=1)\n"
    "print('df_fe shape:', df_fe.shape)\n"
))

# 7. Normalização/Padronização
nb.cells.append(md("## 7. Normalização/Padronização"))
nb.cells.append(code(
    "# Escolha guiada por normalidade (Shapiro p >0.05 => Z-score; caso contrário, Robust)\n"
    "from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler\n"
    "pmap = dict(zip(norm_tbl['col'], norm_tbl.get('shapiro_p', []))) if not norm_tbl.empty else {}\n"
    "scaler_choice = {}\n"
    "for c in df_fe.columns:\n"
    "    if c in pmap and isinstance(pmap[c], (int,float)) and pmap[c]>0.05: scaler_choice[c]='z'\n"
    "    elif df_fe[c].nunique()>2 and df_fe[c].dtype.kind in 'ifc': scaler_choice[c]='robust'\n"
    "    else: scaler_choice[c]='minmax'\n"
    "Z, R, M = StandardScaler(), RobustScaler(), MinMaxScaler()\n"
    "scaled = pd.DataFrame(index=df_fe.index)\n"
    "for c,m in scaler_choice.items():\n"
    "    v = df_fe[[c]].values\n"
    "    if m=='z': scaled[c]=Z.fit_transform(v)\n"
    "    elif m=='robust': scaled[c]=R.fit_transform(v)\n"
    "    else: scaled[c]=M.fit_transform(v)\n"
    "df_scaled = scaled\n"
    "print('df_scaled:', df_scaled.shape)\n"
))

# 8. Feature Engineering Avançado
nb.cells.append(md("## 8. Feature Engineering Avançado"))
nb.cells.append(code(
    "# Exemplos: razões entre pares correlacionados; interações básicas; atributos temporais\n"
    "fe_info = []\n"
    "# 8.1 Razões entre pares com |corr|>0.7 (limitar quantidade)\n"
    "num_cols_sc = [c for c in df_scaled.columns if df_scaled[c].dtype.kind in 'ifc']\n"
    "if len(num_cols_sc)>1:\n"
    "    cm = pd.DataFrame(np.corrcoef(df_scaled[num_cols_sc].T), index=num_cols_sc, columns=num_cols_sc).abs()\n"
    "    pairs = []\n"
    "    for i,a in enumerate(num_cols_sc):\n"
    "        for j,b in enumerate(num_cols_sc):\n"
    "            if j>i and cm.loc[a,b]>0.7 and len(pairs)<5: pairs.append((a,b))\n"
    "    for a,b in pairs:\n"
    "        nm = f'ratio_{a}_over_{b}'\n"
    "        df_scaled[nm] = (df_scaled[a]+1e-9)/(df_scaled[b]+1e-9)\n"
    "        fe_info.append({'feature': nm, 'type':'ratio', 'just':'|corr|>0.7 entre escaladas'})\n"
    "# 8.2 Interações polinomiais simples (quadrático de 3 primeiras)\n"
    "for c in num_cols_sc[:3]:\n"
    "    nm = f'sq_{c}'; df_scaled[nm] = df_scaled[c]**2; fe_info.append({'feature': nm, 'type':'poly2', 'just':'curvaturas potenciais'})\n"
    "# 8.3 Atributos temporais\n"
    "if 'data' in df.columns:\n"
    "    dt = pd.to_datetime(df['data'])\n"
    "    df_scaled['dow'] = dt.dt.dayofweek; df_scaled['month'] = dt.dt.month\n"
    "    fe_info.append({'feature':'dow','type':'temporal','just':'dia da semana'})\n"
    "    fe_info.append({'feature':'month','type':'temporal','just':'mês'})\n"
    "print('Novas features:', len(fe_info))\n"
))

# 9. Validação das Transformações
nb.cells.append(md("## 9. Validação das Transformações"))
nb.cells.append(code(
    "# Conferir distribuições após transformações e correlação final\n"
    "plt.figure(figsize=(8,6));\n"
    "sns.heatmap(df_scaled.corr().clip(-1,1), cmap='vlag', center=0); plt.title('Matriz de correlação final');\n"
    "plt.tight_layout(); plt.savefig(PLOTS_FE_DIR / 'final_corr_heatmap.png', bbox_inches='tight'); plt.show()\n"
))

# 10. Split de Dados (se supervisionado)
nb.cells.append(md("## 10. Split de Dados (se supervisionado)"))
nb.cells.append(code(
    "from sklearn.model_selection import train_test_split\n"
    "if SUPERVISED and TARGET_COL and TARGET_COL in df.columns:\n"
    "    y = df[TARGET_COL]\n"
    "    X = df_scaled.copy()\n"
    "    strat = y if y.nunique()<50 else None\n"
    "    X_train, X_tmp, y_train, y_tmp = train_test_split(X, y, test_size=0.4, random_state=42, stratify=strat)\n"
    "    strat2 = y_tmp if (strat is not None) else None\n"
    "    X_val, X_test, y_val, y_test = train_test_split(X_tmp, y_tmp, test_size=0.5, random_state=42, stratify=strat2)\n"
    "    X_train.assign(**{TARGET_COL: y_train}).to_csv(PROC_DIR / 'train_set.csv', index=False)\n"
    "    X_val.assign(**{TARGET_COL: y_val}).to_csv(PROC_DIR / 'validation_set.csv', index=False)\n"
    "    X_test.assign(**{TARGET_COL: y_test}).to_csv(PROC_DIR / 'test_set.csv', index=False)\n"
    "    print('Splits salvos em data/processed/*.csv')\n"
    "    # Documentar distribuições do alvo por split\n"
    "    import numpy as np, pandas as pd\n"
    "    def stats(s):\n"
    "        s = pd.to_numeric(s, errors='coerce').dropna()\n"
    "        q = s.quantile([0.25,0.5,0.75]) if len(s)>0 else pd.Series([np.nan,np.nan,np.nan], index=[0.25,0.5,0.75])\n"
    "        return {\n"
    "            'n': int(s.shape[0]), 'mean': float(s.mean()) if len(s)>0 else np.nan, 'std': float(s.std()) if len(s)>1 else np.nan,\n"
    "            'min': float(s.min()) if len(s)>0 else np.nan, 'p25': float(q.loc[0.25]), 'p50': float(q.loc[0.5]), 'p75': float(q.loc[0.75]),\n"
    "            'max': float(s.max()) if len(s)>0 else np.nan\n"
    "        }\n"
    "    dist = pd.DataFrame([\n"
    "        dict(split='train', **stats(y_train)),\n"
    "        dict(split='validation', **stats(y_val)),\n"
    "        dict(split='test', **stats(y_test)),\n"
    "    ])\n"
    "    dist.to_csv(TABLES_DIR / 'target_distribution_by_split.csv', index=False)\n"
    "else:\n"
    "    print('SUPERVISED=False ou TARGET_COL ausente; pulando split.')\n"
))

# 11. Dataset Final e Documentação
nb.cells.append(md("## 11. Dataset Final e Documentação"))
nb.cells.append(code(
    "# Salvar dataset final e sumário de transformações\n"
    "final_csv = PROC_DIR / 'features_engineered.csv'\n"
    "df_scaled.to_csv(final_csv, index=False)\n"
    "print('Salvo:', final_csv)\n"
    "# Tabela de features com tipo e transformações aplicadas\n"
    "summary_rows = []\n"
    "for c in df_scaled.columns:\n"
    "    tp = str(df_scaled[c].dtype)\n"
    "    just = []\n"
    "    if c in cfg: just.append(cfg[c].get('justification',''))\n"
    "    if c in transforms: just.append('dist:'+str(transforms[c]))\n"
    "    summary_rows.append({'feature': c, 'dtype': tp, 'justification': '; '.join([j for j in just if j])})\n"
    "fe_summary = pd.DataFrame(summary_rows)\n"
    "fe_summary.to_csv(TABLES_DIR / 'feature_engineering_summary.csv', index=False)\n"
    "print('Resumo salvo em tables/feature_engineering_summary.csv')\n"
))

# 12. Exportação para Modelagem
nb.cells.append(md("## 12. Exportação para Modelagem"))
nb.cells.append(code(
    "# Pipeline reproduzível (JSON simples com escolhas aplicadas)\n"
    "pipe = {\n"
    "  'outliers_config': cfg,\n"
    "  'distribution_transforms': transforms,\n"
    "  'scaler_choice': {c: 'z/robust/minmax'[0:0] for c in []}  # placeholder; escolhas já aplicadas em df_scaled\n"
    "}\n"
    "with open(TABLES_DIR / 'transformation_pipeline.json', 'w', encoding='utf-8') as f:\n"
    "    json.dump(pipe, f, ensure_ascii=False, indent=2)\n"
    "print('Pipeline JSON salvo em tables/transformation_pipeline.json')\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook built at', NB_PATH)

