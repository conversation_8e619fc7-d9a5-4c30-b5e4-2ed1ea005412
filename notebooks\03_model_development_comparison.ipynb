{"cells": [{"cell_type": "markdown", "id": "366fa405", "metadata": {}, "source": ["# Model Comparison - <PERSON><PERSON>\n", "\n", "Comparação de algoritmos com validação cruzada e métricas padronizadas."]}, {"cell_type": "markdown", "id": "b1bd8b10", "metadata": {}, "source": ["## 1. <PERSON>up"]}, {"cell_type": "code", "execution_count": null, "id": "e9679370", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.model_selection import StratifiedKFold, KFold, cross_validate\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, average_precision_score, confusion_matrix, roc_curve, precision_recall_curve, mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.linear_model import LogisticRegression, LinearRegression\n", "from sklearn.svm import SVC, SVR\n", "from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor\n", "from sklearn.neural_network import MLPClassifier, MLPRegressor\n", "import warnings; warnings.filterwarnings('ignore')\n", "BASE = Path('.')\n", "if not (BASE / 'data' / 'processed' / 'features_engineered.csv').exists(): BASE = Path('..')\n", "DATA = BASE / 'data' / 'processed' / 'features_engineered.csv'\n", "REPORTS = BASE / 'reports' / '2025-08-15'\n", "PLOTS = REPORTS / 'plots' / 'model_comparison'\n", "TABLES = REPORTS / 'tables'\n", "for d in [PLOTS, TABLES]: d.mkdir(parents=True, exist_ok=True)\n"]}, {"cell_type": "markdown", "id": "5a5b574e", "metadata": {}, "source": ["## 2. Carregamento de Dados e Tipo de Problema"]}, {"cell_type": "code", "execution_count": null, "id": "8081be92", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(DATA)\n", "# Downsample para execução estável no ambiente local\n", "MAX_N = 5000\n", "if len(df) > MAX_N:\n", "    df = df.sample(MAX_N, random_state=42).reset_index(drop=True)\n", "# Heurística para TARGET: preferir 'valor' se existir; sen<PERSON> colunas típicas; por fim a última\n", "if 'valor' in df.columns:\n", "    TARGET = 'valor'\n", "else:\n", "    possible_targets = [c for c in df.columns if c.lower() in ('target','label','y','classe','class')]\n", "    TARGET = possible_targets[0] if possible_targets else df.columns[-1]\n", "y = df[TARGET]\n", "X = df.drop(columns=[TARGET])\n", "# Usar apenas variáveis numéricas para evitar overhead de codificação durante comparações\n", "X = X.select_dtypes(include=['number'])\n", "# Classificação se y é inteiro com poucas classes; caso contr<PERSON><PERSON> regressão\n", "is_classification = (pd.api.types.is_integer_dtype(y) and y.nunique()<=10) or (y.dtype=='object')\n", "print('TARGET:', TARGET, '| task:', 'classification' if is_classification else 'regression', '| X:', X.shape)\n"]}, {"cell_type": "code", "execution_count": null, "id": "816e49e5", "metadata": {}, "outputs": [], "source": ["# Anti-leakage filtering for supervised training + optional benchmarking wrapper\n", "import re, numpy as np, pandas as pd\n", "from pathlib import Path\n", "\n", "# Ensure TARGET and derived features are excluded from X\n", "base_targets = ['valor','revenue','sales','y']\n", "if 'TARGET' in globals() and TARGET not in base_targets:\n", "    base_targets = [TARGET] + base_targets\n", "\n", "def _is_leak(c: str) -> bool:\n", "    cl = c.lower()\n", "    pats=[]\n", "    for t in base_targets:\n", "        t=t.lower(); pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']\n", "    return any(p in cl for p in pats)\n", "\n", "X = X[[c for c in X.columns if not _is_leak(c)]]\n", "print('X after anti-leak filter:', X.shape)\n", "\n", "# Optional: standardized benchmarking with confidence intervals\n", "try:\n", "    from tools.benchmarking import benchmark_models\n", "    from sklearn.ensemble import RandomForestRegressor\n", "    from sklearn.svm import SVR\n", "    from sklearn.linear_model import LinearRegression\n", "    from sklearn.neural_network import MLPRegressor\n", "    models = {\n", "      'LinReg': LinearRegression(),\n", "      'RF': RandomForestRegressor(n_estimators=200, random_state=42),\n", "      'SVM': SVR(kernel='rbf'),\n", "      'MLP': MLPRegressor(hidden_layer_sizes=(64,32), max_iter=400, random_state=42)\n", "    }\n", "    param_grids = {\n", "      'RF': {'n_estimators':[150,300]},\n", "      'SVM': {'C':[0.5,1], 'gamma':['scale','auto']},\n", "      'MLP': {'hidden_layer_sizes':[(64,32)], 'alpha':[0.0001,0.001]},\n", "    }\n", "    # sample to speed up if large\n", "    Xb = X.copy(); yb = y.copy()\n", "    if len(Xb) > 8000:\n", "        idx = np.random.RandomState(42).choice(len(Xb), 8000, replace=False)\n", "        Xb = Xb.iloc[idx]; yb = yb.iloc[idx]\n", "    dfbench = benchmark_models(Xb, yb, models, param_grids, cv_splits=3)\n", "    dfbench.to_csv(TABLES/'algorithm_ranking_ci.csv', index=False)\n", "    print('Saved benchmarking with CIs ->', TABLES/'algorithm_ranking_ci.csv')\n", "except Exception as e:\n", "    print('Benchmarking wrapper skipped ->', e)\n"]}, {"cell_type": "markdown", "id": "d8e21f5e", "metadata": {}, "source": ["## 3. Modelos e Validação Cruzada"]}, {"cell_type": "code", "execution_count": null, "id": "82a362e9", "metadata": {}, "outputs": [], "source": ["np.random.seed(42)\n", "if is_classification:\n", "    models = {\n", "        'LogReg': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', LogisticRegression(max_iter=200))]),\n", "        'RF': RandomForestClassifier(n_estimators=200, random_state=42),\n", "        'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', SVC(kernel='rbf', probability=True, random_state=42))]),\n", "        'GB': RandomForestClassifier(n_estimators=400, max_depth=None, random_state=42),\n", "        'MLP': MLPClassifier(hidden_layer_sizes=(64,32), max_iter=300, random_state=42)\n", "    }\n", "    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "    scoring = ['accuracy','precision_weighted','recall_weighted','f1_weighted','roc_auc_ovr']\n", "else:\n", "    models = {\n", "        'LinReg': LinearRegression(),\n", "        'RF': RandomForestRegressor(n_estimators=300, random_state=42),\n", "        'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('svr', SVR(kernel='rbf'))]),\n", "        'GB': RandomForestRegressor(n_estimators=600, max_depth=None, random_state=42),\n", "        'MLP': MLPRegressor(hidden_layer_sizes=(64,32), max_iter=400, random_state=42)\n", "    }\n", "    cv = KFold(n_splits=5, shuffle=True, random_state=42)\n", "    scoring = {'rmse': 'neg_root_mean_squared_error', 'mae': 'neg_mean_absolute_error', 'r2': 'r2'}\n"]}, {"cell_type": "markdown", "id": "23bb5da4", "metadata": {}, "source": ["## 4. Validação Cruzada e Métricas"]}, {"cell_type": "code", "execution_count": null, "id": "59885169", "metadata": {}, "outputs": [], "source": ["results = []\n", "for name, model in models.items():\n", "    cvres = cross_validate(model, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n", "    res = { 'model': name }\n", "    for k,v in cvres.items():\n", "        if k.startswith('test_'):\n", "            mname = k.replace('test_','')\n", "            res[mname+'_mean'] = float(np.mean(v))\n", "            res[mname+'_std'] = float(np.std(v))\n", "    results.append(res)\n", "res_df = pd.DataFrame(results)\n", "res_df.to_csv(TABLES / 'algorithm_ranking.csv', index=False)\n", "res_df\n"]}, {"cell_type": "markdown", "id": "7b025253", "metadata": {}, "source": ["## 5. Visualizações de Performance"]}, {"cell_type": "code", "execution_count": null, "id": "c3077aa8", "metadata": {}, "outputs": [], "source": ["PLOTS.mkdir(parents=True, exist_ok=True)\n", "if is_classification:\n", "    # Boxplot F1-weighted como exemplo\n", "    # (cross_validate não retorna valores fold-by-fold diretamente por métrica nomeada, simplificamos com o df de summary)\n", "    plt.figure(figsize=(6,4))\n", "    sns.barplot(data=res_df, x='model', y='f1_weighted_mean')\n", "    plt.title('F1 (weighted) médio por modelo'); plt.tight_layout(); plt.savefig(PLOTS / 'bar_f1.png'); plt.show()\n", "else:\n", "    plt.figure(figsize=(6,4))\n", "    sns.barplot(data=res_df, x='model', y='rmse_mean')\n", "    plt.title('RMSE médio por modelo'); plt.tight_layout(); plt.savefig(PLOTS / 'bar_rmse.png'); plt.show()\n"]}, {"cell_type": "markdown", "id": "a8336da9", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "6e1bca95", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "recs = []\n", "if is_classification:\n", "    # <PERSON><PERSON> por F1\n", "    best = res_df.sort_values('f1_weighted_mean', ascending=False).iloc[0]\n", "    recs.append({'scenario':'equilíbrio precisão/recall','recommended': best['model'],'metric': 'f1_weighted_mean', 'value': float(best['f1_weighted_mean'])})\n", "else:\n", "    best = res_df.sort_values('rmse_mean', ascending=True).iloc[0]\n", "    recs.append({'scenario':'menor erro','recommended': best['model'], 'metric': 'rmse_mean', 'value': float(best['rmse_mean'])})\n", "rec_df = pd.DataFrame(recs)\n", "rec_df.to_csv(TABLES/'model_recommendations.csv', index=False)\n", "rec_df\n"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}