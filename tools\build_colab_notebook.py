import nbformat as nbf
from pathlib import Path

def md(t):
    return nbf.v4.new_markdown_cell(t)

def code(t):
    return nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
SRC_NB = BASE / 'notebooks' / 'chilli_beans_analysis.ipynb'
OUT_NB = BASE / 'notebooks' / 'chilli_beans_analysis_colab.ipynb'

nb_src = nbf.read(open(SRC_NB, 'r', encoding='utf-8'), as_version=4)
nb = nbf.v4.new_notebook()

# Header and Colab setup
nb.cells.append(md(
    "# ChilliAnalyzer – Notebook Principal (Google Colab)\n\n"
    "Este notebook foi preparado para execução no Google Colab e cobre o pipeline principal (EDA → Feature Engineering → Modelagem), com explicações acadêmicas conforme barema."
))

nb.cells.append(code(
    "#@title Configuração do ambiente (Colab)\n"
    "import sys, subprocess\n"
    "IN_COLAB = 'google.colab' in sys.modules\n"
    "if IN_COLAB:\n"
    "    pkgs = ['pandas>=1.5.0','numpy>=1.21.0','scipy>=1.9.0','scikit-learn>=1.1.0',\n"
    "            'seaborn>=0.12.0','matplotlib>=3.5.0','statsmodels>=0.13.0',\n"
    "            'plotly>=5.0.0','folium>=0.14.0','xgboost>=1.7.0']\n"
    "    print('Instalando dependências...')\n"
    "    subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-q'] + pkgs)\n"
    "    try:\n"
    "        from google.colab import drive\n"
    "        # drive.mount('/content/drive')  # opcional\n"
    "    except Exception:\n"
    "        pass\n"
))

nb.cells.append(md(
    "## 1. Introdução e Modelagem do Problema\n\n"
    "Contexto: A Chilli Beans busca alavancar dados para apoiar decisões de negócio em expansão territorial, segmentação de clientes e aumento de vendas de lentes de grau.\n\n"
    "Formulação: Adotamos uma abordagem híbrida (supervisionado + não-supervisionado).\n\n"
    "- Supervisionado: modelagem preditiva (ex.: receita/potencial) usando Random Forest, com validação cruzada e métricas RMSE/MAE/R².\n"
    "- Não-supervisionado: segmentação de clientes via K-Means, com avaliação por Silhouette e visualização por PCA.\n\n"
    "Objetivos específicos:\n"
    "1) EDA para entendimento das variáveis e qualidade dos dados;\n"
    "2) Preparação de dados com feature engineering e normalização robusta;\n"
    "3) Treinamento e avaliação de modelos;\n"
    "4) Interpretação dos resultados e recomendações de negócio.\n"
))

nb.cells.append(md(
    "## 2. Seleção e Justificativa das Features\n\n"
    "- Exclusão de colunas de ID das análises/modelagem (usadas apenas para cardinalidade/joins).\n"
    "- Tratamento de outliers (winsorização/clipping por sigma/IQR) e transformações de distribuição (log1p/Box-Cox) para reduzir assimetria.\n"
    "- Normalização: z-score quando normalidade plausível (Shapiro p>0,05); caso contrário, escalonamento robusto; min-max em casos pontuais.\n"
    "- Seleção guiada por colinearidade (|corr|) e relevância de negócio.\n"
))

nb.cells.append(md(
    "## 2.1 Split Treino/Validação/Teste (60/20/20)\n\n"
    "- Split estratificado por distribuição do alvo (quando aplicável).\n"
    "- Arquivos gerados em `data/processed/`: train_set.csv, validation_set.csv, test_set.csv.\n"
    "- Tabela `target_distribution_by_split.csv` documenta tamanho e estatísticas do alvo por split.\n"
))

nb.cells.append(md(
    "## 3. Implementação dos Modelos\n\n"
    "- Regressão (Random Forest): hiperparâmetros base (n_estimators, max_depth, min_samples_leaf), validação cruzada 5-fold.\n"
    "- Clustering (K-Means): seleção de K em {4..6} por maior Silhouette; PCA para visualização 2D.\n"
    "- Classificação (XGBoost/GB) para propensão a grau: ROC-AUC, Average Precision e F1.\n"
))

nb.cells.append(md(
    "## 4. Métricas e Avaliação\n\n"
    "- Regressão: RMSE (erro quadrático médio na escala do alvo), MAE (erro absoluto médio), R² (proporção da variância explicada).\n"
    "- Clustering: Silhouette (cohesão/separação), Inertia (compacidade intra-cluster), Davies-Bouldin (separação relativa; menor é melhor).\n"
))

nb.cells.append(md(
    "## 5. Discussão dos Resultados\n\n"
    "Análise crítica da performance, limitações (ex.: dados ausentes de demografia/concorrência), riscos de overfitting e implicações práticas (ex.: priorização de regiões, estratégias por segmento).\n"
))

nb.cells.append(md(
    "## 6. Conclusões\n\n"
    "Resumo de achados, recomendações implementáveis e próximos passos (ex.: enriquecimento de dados externos, explicabilidade via SHAP, automação de retreinamento).\n"
))

# Importar e executar o pipeline original
nb.cells.append(md("---\n### Execução do Pipeline (células originais)"))
nb.cells.extend(nb_src.cells)

# Carregar splits e avaliar no teste
nb.cells.append(md("---\n### Avaliação com splits (carregamento e métricas de teste)"))
nb.cells.append(code(
    "from pathlib import Path\nimport pandas as pd, numpy as np\nBASE=Path('.')\nif not (BASE/'data'/'processed'/'train_set.csv').exists(): BASE=Path('..')\nP=BASE/'data'/'processed'\ntrain=pd.read_csv(P/'train_set.csv'); val=pd.read_csv(P/'validation_set.csv'); test=pd.read_csv(P/'test_set.csv')\nTARGET='valor' if 'valor' in train.columns else train.columns[-1]\nXtr=train.drop(columns=[TARGET]); ytr=train[TARGET]\nXva=val.drop(columns=[TARGET]); yva=val[TARGET]\nXte=test.drop(columns=[TARGET]); yte=test[TARGET]\nfrom sklearn.ensemble import RandomForestRegressor\nfrom sklearn.metrics import mean_absolute_error, r2_score\nrf=RandomForestRegressor(n_estimators=400, random_state=42, n_jobs=-1)\nrf.fit(pd.get_dummies(Xtr), ytr)\npred=rf.predict(pd.get_dummies(Xte).reindex(columns=pd.get_dummies(Xtr).columns, fill_value=0))\nrmse=float(np.sqrt(((pred-yte)**2).mean())); mae=float(mean_absolute_error(yte,pred)); r2=float(r2_score(yte,pred))\nprint('TEST RMSE:',rmse,'MAE:',mae,'R2:',r2)\n"))

# Write
OUT_NB.parent.mkdir(parents=True, exist_ok=True)
with open(OUT_NB, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook built at', OUT_NB)

