from pathlib import Path
import nbformat as nbf
import sys

path = Path(sys.argv[1]) if len(sys.argv)>1 else None
if not path or not path.exists():
    print('Usage: python tools/normalize_notebook.py <notebook.ipynb>')
    raise SystemExit(1)

nb = nbf.read(path, as_version=4)
changed = False
for c in nb.cells:
    if c.get('cell_type') == 'code':
        if 'outputs' not in c:
            c['outputs'] = []
            changed = True
        if 'execution_count' not in c:
            c['execution_count'] = None
            changed = True
        if 'metadata' not in c:
            c['metadata'] = {}
            changed = True

if changed:
    nbf.write(nb, path)
    print('Normalized', path)
else:
    print('No changes for', path)

