{"cells": [{"cell_type": "markdown", "id": "ba8f79e3", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>s (Colab)\n", "\n", "> Notebook preparado para execução no Google Colab. Integra os resultados em `reports/2025-08-15/`."]}, {"cell_type": "code", "execution_count": null, "id": "c0ea4491", "metadata": {}, "outputs": [], "source": ["#@title Preparação do Ambiente\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from warnings import filterwarnings; filterwarnings('ignore')\n", "BASE_DIR = Path('.')\n", "# Fallback quando executado a partir da pasta notebooks\n", "if not (BASE_DIR / 'data' / 'clean' / 'cleaned_featured.csv').exists():\n", "    BASE_DIR = Path('..')\n", "REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'\n", "PLOTS_DIR = REPORTS_DIR / 'plots'\n", "EDA_PLOTS_DIR = PLOTS_DIR / 'eda'\n", "TABLES_DIR = REPORTS_DIR / 'tables'\n", "CLEAN_DATA = BASE_DIR / 'data' / 'clean' / 'cleaned_featured.csv'\n", "print('PLOTS_DIR:', PLOTS_DIR)\n", "print('EDA_PLOTS_DIR:', EDA_PLOTS_DIR)\n", "print('TABLES_DIR:', TABLES_DIR)\n", "print('CLEAN_DATA:', CLEAN_DATA)\n", "\n", "# Atualiza sys.path para permitir imports do pacote src/\n", "import sys\n", "PROJ_ROOT = BASE_DIR.resolve()\n", "SRC_DIR = PROJ_ROOT / 'src'\n", "for p in [str(PROJ_ROOT), str(SRC_DIR)]:\n", "    if p not in sys.path:\n", "        sys.path.insert(0, p)\n", "print('sys.path atualizado com', SRC_DIR)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c399a708", "metadata": {}, "outputs": [], "source": ["#@title <PERSON><PERSON><PERSON>ída (plots/tables)\n", "PLOTS_DIR.mkdir(parents=True, exist_ok=True)\n", "EDA_PLOTS_DIR.mkdir(parents=True, exist_ok=True)\n", "TABLES_DIR.mkdir(parents=True, exist_ok=True)\n", "print('Diretórios garantidos:', PLOTS_DIR, EDA_PLOTS_DIR, TABLES_DIR)\n"]}, {"cell_type": "code", "execution_count": null, "id": "94ddf813", "metadata": {}, "outputs": [], "source": ["#@title Diretório para gráficos de outliers\n", "OUTLIERS_DIR = EDA_PLOTS_DIR / 'outliers'\n", "OUTLIERS_DIR.mkdir(parents=True, exist_ok=True)\n", "print('OUTLIERS_DIR:', OUTLIERS_DIR)\n"]}, {"cell_type": "code", "execution_count": null, "id": "2300a781", "metadata": {}, "outputs": [], "source": ["#@title Teste de imports do pacote src\n", "mods = ['analysis_stats','baselines','features','geo_utils','io_utils','metrics']\n", "for m in mods:\n", "    try:\n", "        __import__(f'src.{m}')\n", "        print(f'OK: src.{m}')\n", "    except Exception as e:\n", "        print(f'FALHA: src.{m} -> {e}')\n"]}, {"cell_type": "code", "execution_count": null, "id": "7c5a5a2e", "metadata": {}, "outputs": [], "source": ["#@title (Opcional) Fixar versões para reprodutibilidade (Colab)\n", "DO_PIP = False\n", "if DO_PIP:\n", "    import IPython\n", "    IPython.get_ipython().run_line_magic('pip', 'install -q pandas>=1.5.0 numpy>=1.21.0 scipy>=1.9.0 scikit-learn>=1.1.0 seaborn>=0.12.0 matplotlib>=3.5.0 statsmodels>=0.13.0')\n", "import numpy as np\n", "RANDOM_SEED = 42\n", "np.random.seed(RANDOM_SEED)\n", "print('Random seed set to', RANDOM_SEED)\n"]}, {"cell_type": "markdown", "id": "6afbe9ad", "metadata": {}, "source": ["## 1. Exploração de Dados (EDA)\n", "- Variáveis numéricas: `valor`, `qtd`\n", "- Categóricas: `uf`, `cidade`, `Tipo_PDV`\n", "- Gráficos: <PERSON><PERSON><PERSON> por UF; <PERSON><PERSON>ita média por dia da semana; Top 20 cidades; Correla<PERSON>pearman (valor×qtd)"]}, {"cell_type": "code", "execution_count": null, "id": "63f062bc", "metadata": {}, "outputs": [], "source": ["#@title Carregamento dos dados limpos\n", "df = pd.read_csv(CLEAN_DATA, parse_dates=['data'])\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": null, "id": "b1e00ab9", "metadata": {}, "outputs": [], "source": ["#@title Identificar colunas de ID e definir variáveis numéricas úteis\n", "import re\n", "def is_id_col(name: str) -> bool:\n", "    n = str(name).strip()\n", "    nl = n.lower().replace(' ', '')\n", "    # Padrões comuns de ID e nomes específicos do domínio\n", "    if nl in {'id','id_loja','id_produto','id_cliente','id_vendedor','id_faturamento','documento','transacao'}:\n", "        return True\n", "    return bool(re.match(r'^(id_|.*_id$|id$|id\\.)', nl))\n", "NUM_ALL = [c for c in df.select_dtypes(include=['number']).columns]\n", "ID_COLS = [c for c in NUM_ALL if is_id_col(c)]\n", "FEAT_NUM = [c for c in NUM_ALL if c not in ID_COLS and df[c].nunique()>2]\n", "print('IDs detectadas:', ID_COLS[:10], '... (total', len(ID_COLS), ')')\n", "print('Numé<PERSON>s <PERSON> (sem IDs):', FEAT_NUM[:10], '... (total', len(FEAT_NUM), ')')\n", "# Cardinalidade de IDs (mantidos apenas para contagem/join)\n", "if ID_COLS:\n", "    card = {c:int(df[c].nunique()) for c in ID_COLS}\n", "    card_df = pd.DataFrame.from_dict(card, orient='index', columns=['n_unique']).sort_values('n_unique', ascending=False)\n", "    display(card_df.head(10))\n"]}, {"cell_type": "code", "execution_count": null, "id": "d0a1bef6", "metadata": {}, "outputs": [], "source": ["#@title Estatísticas descritivas e frequências\n", "num_cols = [c for c in ['valor','qtd'] if c in df.columns]\n", "cat_cols = [c for c in ['uf','cidade','Tipo_PDV'] if c in df.columns]\n", "display(df[num_cols].describe().T)\n", "for c in cat_cols:\n", "    display(pd.DataFrame({'freq': df[c].value_counts().head(10),\n", "                          'pct': df[c].value_counts(normalize=True).head(10)*100}))\n"]}, {"cell_type": "code", "execution_count": null, "id": "68ca405a", "metadata": {}, "outputs": [], "source": ["#@title (EDA) Matriz de correlação (Spearman) e exportação\n", "num_cols_avail = [c for c in ['valor','qtd'] if c in df.columns]\n", "if len(num_cols_avail) >= 2:\n", "    corr = df[num_cols_avail].corr(method='spearman')\n", "    fig = plt.figure(figsize=(5,4))\n", "    sns.heatmap(corr, annot=True, cmap='coolwarm', vmin=-1, vmax=1)\n", "    plt.title('<PERSON><PERSON> (S<PERSON><PERSON>)')\n", "    plt.tight_layout()\n", "    try:\n", "        out_path = PLOTS_DIR / 'heatmap_corr.png'\n", "        fig.savefig(out_path, bbox_inches='tight')\n", "        print('Salvo:', out_path)\n", "    except Exception as e:\n", "        print('Aviso: não foi possível salvar heatmap_corr.png ->', e)\n", "    plt.show()\n", "else:\n", "    print('Variáveis numéricas insuficientes para correlação.')\n"]}, {"cell_type": "code", "execution_count": null, "id": "6756860d", "metadata": {}, "outputs": [], "source": ["#@title Re<PERSON>ita por UF; Receita média por dia; Top 20 cidades\n", "# Receita por UF\n", "revenue_uf = df.groupby('uf')['valor'].sum().sort_values(ascending=False).reset_index()\n", "plt.figure(figsize=(12,5));\n", "sns.barplot(data=revenue_uf, x='uf', y='valor', palette='Blues_r');\n", "plt.title('<PERSON><PERSON><PERSON> por UF (centavos)'); plt.xticks(rotation=45); plt.tight_layout(); plt.show()\n", "# Re<PERSON>ita média por dia da semana\n", "df['dow'] = df['data'].dt.dayofweek\n", "dow_map = {0:'Seg',1:'<PERSON><PERSON>',2:'Qua',3:'Qui',4:'Sex',5:'<PERSON><PERSON><PERSON>',6:'Dom'}\n", "rev_dow = df.groupby('dow')['valor'].mean().rename(index=dow_map).reset_index()\n", "plt.figure(figsize=(8,4)); sns.barplot(data=rev_dow, x='dow', y='valor', palette='viridis');\n", "plt.title('<PERSON><PERSON><PERSON> por <PERSON> Semana'); plt.tight_layout(); plt.show()\n", "# Top 20 cidades\n", "top_cities = df.groupby(['uf','cidade'])['valor'].sum().reset_index().sort_values('valor', ascending=False).head(20)\n", "plt.figure(figsize=(10,8)); sns.barplot(data=top_cities, y='cidade', x='valor', hue='uf', dodge=False, palette='mako');\n", "plt.title('Top 20 Cidades por Receita'); plt.tight_layout(); plt.show()\n"]}, {"cell_type": "markdown", "id": "f9228d9f", "metadata": {}, "source": ["### 1.1 <PERSON><PERSON><PERSON><PERSON>ivariad<PERSON>\n", "\n", "- Histogramas com escalas e transformações (linear, log1p, sqrt, Box-Cox)\n", "- Boxplots/Violin e densidade\n", "- Exportação para reports/2025-08-15/plots/eda\n"]}, {"cell_type": "code", "execution_count": null, "id": "bc5d28c8", "metadata": {}, "outputs": [], "source": ["#@title Histogramas e transformações (linear, log1p, sqrt, Box-Cox)\n", "from scipy.stats import boxcox\n", "import numpy as np\n", "num_cols = FEAT_NUM.copy()\n", "for c in num_cols[:8]:  # limitar para evitar excesso de figuras\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if s.empty:\n", "        continue\n", "    fig, axes = plt.subplots(1, 3, figsize=(15,4))\n", "    sns.histplot(s, kde=True, ax=axes[0]); axes[0].set_title(f'{c} - linear')\n", "    sns.histplot(np.log1p(s.clip(lower=0)), kde=True, ax=axes[1]); axes[1].set_title(f'{c} - log1p')\n", "    sns.histplot(np.sqrt(s.clip(lower=0)), kde=True, ax=axes[2]); axes[2].set_title(f'{c} - sqrt')\n", "    plt.tight_layout()\n", "    try:\n", "        fig.savefig(EDA_PLOTS_DIR / f'hist_{c}_linear_log_sqrt.png', bbox_inches='tight')\n", "    except Exception as e:\n", "        print('Falha ao salvar hist linear/log/sqrt', c, e)\n", "    plt.show()\n", "    # Box-Cox exige valores positivos\n", "    sp = s[s > 0]\n", "    if len(sp) > 10:\n", "        try:\n", "            bc, lam = boxcox(sp)\n", "            plt.figure(figsize=(5,4)); sns.histplot(bc, kde=True); plt.title(f'{c} - Box-Cox (lambda={lam:.2f})');\n", "            plt.tight_layout()\n", "            (EDA_PLOTS_DIR / 'boxcox').mkdir(exist_ok=True)\n", "            plt.savefig(EDA_PLOTS_DIR / 'boxcox' / f'hist_{c}_boxcox.png', bbox_inches='tight')\n", "            plt.show()\n", "        except Exception as e:\n", "            print('Box-<PERSON> indisponível para', c, '->', e)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9efd0f97", "metadata": {}, "outputs": [], "source": ["#@title Box/Violin e densidade por variável e categoria\n", "cat_pref = 'Tipo_PDV' if 'Tipo_PDV' in df.columns else ('uf' if 'uf' in df.columns else None)\n", "if cat_pref:\n", "    top_vals = df[cat_pref].value_counts().head(5).index.tolist()\n", "    sub = df[df[cat_pref].isin(top_vals)].sample(min(5000, df.shape[0]), random_state=42).copy()\n", "    for c in num_cols[:6]:\n", "        plt.figure(figsize=(10,4));\n", "        plt.subplot(1,2,1); sns.violinplot(data=sub, x=cat_pref, y=c, inner='quartile'); plt.title(f'Violin: {c} por {cat_pref}')\n", "        plt.subplot(1,2,2);\n", "        for v in top_vals:\n", "            sns.kdeplot(pd.to_numeric(sub.loc[sub[cat_pref]==v, c], errors='coerce'), label=str(v), warn_singular=False)\n", "        plt.legend(); plt.title(f'Densidade: {c} por {cat_pref}')\n", "        plt.tight_layout()\n", "        try:\n", "            plt.savefig(EDA_PLOTS_DIR / f'violin_density_{c}_by_{cat_pref}.png', bbox_inches='tight')\n", "        except Exception as e:\n", "            print('<PERSON><PERSON><PERSON> ao salvar violin/density', c, e)\n", "        plt.show()\n"]}, {"cell_type": "markdown", "id": "fd80d003", "metadata": {}, "source": ["### 1.2 Correlações Expandida\n", "\n", "- <PERSON>, <PERSON><PERSON><PERSON> e <PERSON>\n", "- Pairplots por categoria (amostragem)\n", "- Clustering hierárquico de correlações\n"]}, {"cell_type": "code", "execution_count": null, "id": "ef14c19e", "metadata": {}, "outputs": [], "source": ["#@title Heatmaps de correlação (<PERSON>/<PERSON>/<PERSON>)\n", "num_cols2 = FEAT_NUM.copy()\n", "methods = ['pearson','spearman','kendall']\n", "for m in methods:\n", "    try:\n", "        corr = df[num_cols2].corr(method=m)\n", "        plt.figure(figsize=(min(12, 2+0.5*len(num_cols2)), min(10, 2+0.5*len(num_cols2))))\n", "        sns.heatmap(corr, cmap='vlag', center=0)\n", "        plt.title(f'<PERSON><PERSON> ({m.title()})')\n", "        plt.tight_layout()\n", "        plt.savefig(EDA_PLOTS_DIR / f'heatmap_corr_{m}.png', bbox_inches='tight')\n", "        plt.show()\n", "    except Exception as e:\n", "        print('Falha correlação', m, '->', e)\n"]}, {"cell_type": "code", "execution_count": null, "id": "34848b0d", "metadata": {}, "outputs": [], "source": ["#@title Pairplot (amostra) segmentado por categoria\n", "try:\n", "    sample = df.sample(min(1500, len(df)), random_state=42) if len(df) > 1500 else df.copy()\n", "    hue = cat_pref if cat_pref in sample.columns else None\n", "    sub_cols = num_cols2[:4]  # limitar\n", "    g = sns.pairplot(sample[sub_cols + ([hue] if hue else [])], hue=hue, diag_kind='kde', corner=True)\n", "    g.fig.suptitle('Pairplot (amostra)'+ (f' por {hue}' if hue else ''), y=1.02)\n", "    g.savefig(EDA_PLOTS_DIR / 'pairplot_sample.png', bbox_inches='tight')\n", "    plt.show()\n", "except Exception as e:\n", "    print('Pairplot indisponível ->', e)\n"]}, {"cell_type": "code", "execution_count": null, "id": "96f26a59", "metadata": {}, "outputs": [], "source": ["#@title Clustermap de correlação (Spearman)\n", "try:\n", "    corr_s = df[num_cols2].corr(method='spearman')\n", "    g = sns.clustermap(corr_s, cmap='coolwarm', center=0)\n", "    g.fig.suptitle('<PERSON><PERSON><PERSON><PERSON> (Spearman)')\n", "    g.savefig(EDA_PLOTS_DIR / 'clustermap_spearman.png', bbox_inches='tight')\n", "    plt.show()\n", "except Exception as e:\n", "    print('Clustermap falhou ->', e)\n"]}, {"cell_type": "markdown", "id": "e7c8bb4a", "metadata": {}, "source": ["### 1.3 Detecção e Visualização de Outliers\n", "\n", "- Scatterplots com destaque de outliers (robust Z e Mahalanobis)\n", "- Boxplots segmentados por grupos\n", "- Comparativos antes/depois da remoção\n"]}, {"cell_type": "code", "execution_count": null, "id": "30fe2bb1", "metadata": {}, "outputs": [], "source": ["#@title Outliers: robust <PERSON> e Mahalanobis\n", "import numpy as np\n", "num_for_out = [c for c in FEAT_NUM if df[c].nunique()>5][:6]\n", "sub = df[num_for_out].dropna()\n", "# Robust Z por coluna\n", "def robust_z_s(x):\n", "    med = np.median(x); mad = np.median(np.abs(x-med));\n", "    return 0.6745*(x-med)/(mad if mad>0 else 1.0)\n", "rz = sub.apply(robust_z_s)\n", "rz_any = (np.abs(rz) >= 3).any(axis=1)\n", "# <PERSON><PERSON><PERSON><PERSON>\n", "X = sub.values\n", "mu = X.mean(axis=0)\n", "cov = np.cov(X, rowvar=False)\n", "# regularização leve para matriz singular\n", "cov += np.eye(cov.shape[0])*1e-6\n", "inv = np.linalg.pinv(cov)\n", "d2 = np.einsum('ij,jk,ik->i', X-mu, inv, X-mu)\n", "thr = np.quantile(d2, 0.995)\n", "mh = d2 >= thr\n", "out_mask = rz_any | mh\n", "# Esco<PERSON>her duas features para scatter\n", "pair = num_for_out[:2] if len(num_for_out)>=2 else num_for_out\n", "if len(pair)==2:\n", "    plt.figure(figsize=(6,5))\n", "    plt.scatter(sub[pair[0]], sub[pair[1]], s=8, alpha=0.3, label='normal')\n", "    plt.scatter(sub.loc[out_mask, pair[0]], sub.loc[out_mask, pair[1]], s=12, color='r', alpha=0.6, label='outlier')\n", "    plt.xlabel(pair[0]); plt.ylabel(pair[1]); plt.title('Scatter com outliers (robustZ/Mahalanobis)'); plt.legend()\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / 'scatter_outliers.png', bbox_inches='tight'); plt.show()\n", "# Antes/depois em uma métrica alvo\n", "target = 'valor' if 'valor' in df.columns else (num_for_out[0] if num_for_out else None)\n", "if target:\n", "    fig, axes = plt.subplots(1,2, figsize=(10,4))\n", "    sns.boxplot(y=df[target], ax=axes[0]); axes[0].set_title(f'{target} (bruto)')\n", "    sns.boxplot(y=sub.loc[~out_mask, target] if target in sub.columns else pd.Series(dtype=float), ax=axes[1]); axes[1].set_title(f'{target} (sem outliers)')\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / f'box_before_after_{target}.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "markdown", "id": "147e813c", "metadata": {}, "source": ["#### 1.3.1 Análise de Normalidade Visual (limitado por amostra)\n", "\n", "Inclui: Q-Q plots, histogramas com curva normal ajustada, testes de normalidade (<PERSON>, <PERSON>) e densidade empírica vs. normal teórica.\n"]}, {"cell_type": "code", "execution_count": null, "id": "8d665f49", "metadata": {}, "outputs": [], "source": ["#@title Q-Q plots e histograma com curva normal\n", "import numpy as np\n", "from scipy import stats\n", "try:\n", "    import statsmodels.api as sm\n", "except Exception:\n", "    sm = None\n", "num_cols_n = [c for c in FEAT_NUM if df[c].nunique()>10][:6]\n", "for c in num_cols_n:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if len(s) < 20:\n", "        print(f'{c}: amostra muito pequena para avaliação visual robusta (n={len(s)})')\n", "        continue\n", "    # Q-Q plot\n", "    if sm is not None:\n", "        fig = sm.qqplot(s, line='s'); plt.title(f'QQ-plot: {c}');\n", "        fig.savefig(OUTLIERS_DIR / f'qqplot_{c}.png', bbox_inches='tight'); plt.show()\n", "    else:\n", "        print('statsmodels ausente; pulando QQ-plot.')\n", "    # Hist sobreposto com normal teórica\n", "    mu, sigma = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "    xs = np.linspace(s.min(), s.max(), 200)\n", "    norm_pdf = stats.norm.pdf(xs, loc=mu, scale=sigma)\n", "    plt.figure(figsize=(6,4))\n", "    sns.histplot(s, kde=False, stat='density', bins='auto', color='steelblue', alpha=0.6)\n", "    plt.plot(xs, norm_pdf, 'r-', label=f'N({mu:.2f}, {sigma:.2f})')\n", "    plt.legend(); plt.title(f'Histograma vs. Normal: {c}')\n", "    plt.tight_layout(); plt.savefig(OUTLIERS_DIR / f'hist_normal_{c}.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "159129f8", "metadata": {}, "outputs": [], "source": ["#@title Testes de normalidade e densidade empírica vs. normal\n", "from scipy import stats\n", "records = []\n", "for c in num_cols_n:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if len(s) >= 8:\n", "        w_stat, w_p = stats.shapiro(s.sample(min(5000, len(s)), random_state=42))\n", "        ad = stats.anderson(s, dist='norm')\n", "        records.append({'col': c, 'n': len(s), 'shapiro_<PERSON>': w_stat, 'shapiro_p': w_p, 'anderson_stat': ad.statistic})\n", "        # Densidade empírica vs. normal\n", "        mu, sigma = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "        xs = np.linspace(s.quantile(0.01), s.quantile(0.99), 200)\n", "        norm_pdf = stats.norm.pdf(xs, loc=mu, scale=sigma)\n", "        plt.figure(figsize=(6,4))\n", "        sns.kdeplot(s, label='Empírica', warn_singular=False)\n", "        plt.plot(xs, norm_pdf, 'r--', label='Normal teórica')\n", "        plt.legend(); plt.title(f'Densidade empírica vs. normal: {c}')\n", "        plt.tight_layout(); plt.savefig(OUTLIERS_DIR / f'kde_vs_normal_{c}.png', bbox_inches='tight'); plt.show()\n", "stats_tbl = pd.DataFrame.from_records(records)\n", "display(stats_tbl)\n", "stats_path = TABLES_DIR / 'normality_tests.csv'\n", "stats_tbl.to_csv(stats_path, index=False)\n", "print('Salvo:', stats_path)\n"]}, {"cell_type": "markdown", "id": "6382536a", "metadata": {}, "source": ["#### 1.3.2 Outliers com Curvas de Referência\n", "\n", "Inclui: boxplots com limites (IQR, 2-3σ), elipses de confiança bivariadas, resíduos padronizados e marcação de >2-3σ.\n"]}, {"cell_type": "code", "execution_count": null, "id": "29dfa26b", "metadata": {}, "outputs": [], "source": ["#@title Boxplots com IQR e bandas de desvio-padrão\n", "import numpy as np\n", "cols = num_cols_n[:4] if 'num_cols_n' in globals() else FEAT_NUM[:4]\n", "for c in cols:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if s.empty: continue\n", "    q1, q3 = s.quantile(0.25), s.quantile(0.75)\n", "    iqr = q3 - q1\n", "    low_iqr, high_iqr = q1 - 1.5*iqr, q3 + 1.5*iqr\n", "    mu, sd = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "    plt.figure(figsize=(7,4))\n", "    sns.boxplot(x=s, orient='h')\n", "    for k, col in [(2,'orange'), (3,'red')]:\n", "        plt.axvline(mu + k*sd, color=col, linestyle='--', alpha=0.7)\n", "        plt.axvline(mu - k*sd, color=col, linestyle='--', alpha=0.7)\n", "    plt.axvspan(low_iqr, high_iqr, color='green', alpha=0.08, label='±1.5 IQR')\n", "    plt.title(f'Boxplot {c} com limites teóricos (±1.5 IQR, ±2σ, ±3σ)')\n", "    plt.tight_layout(); plt.savefig(OUTLIERS_DIR / f'box_theoretical_{c}.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "b2c52aa5", "metadata": {}, "outputs": [], "source": ["#@title Scatter com elipses de confiança bivariadas\n", "from matplotlib.patches import Ellipse\n", "pair = None\n", "nums = [c for c in df.select_dtypes(include=['number']).columns]\n", "if len(nums)>=2: pair = nums[:2]\n", "if pair:\n", "    x = pd.to_numeric(df[pair[0]], errors='coerce').dropna()\n", "    y = pd.to_numeric(df[pair[1]], errors='coerce').dropna()\n", "    n = min(len(x), len(y))\n", "    x, y = x.iloc[:n], y.iloc[:n]\n", "    X = np.column_stack([x, y])\n", "    mu = X.mean(axis=0); cov = np.cov(X, rowvar=False) + np.eye(2)*1e-6\n", "    vals, vecs = np.linalg.eigh(cov)\n", "    order = vals.argsort()[::-1]\n", "    vals, vecs = vals[order], vecs[:, order]\n", "    theta = np.degrees(np.arctan2(*vecs[:,0][::-1]))\n", "    fig, ax = plt.subplots(figsize=(6,5))\n", "    ax.scatter(x, y, s=8, alpha=0.3)\n", "    # Elipses ~ 1σ, 2σ, 3σ (aprox)\n", "    for k, col in [(1,'#2ca02c'),(2,'#ff7f0e'),(3,'#d62728')]:\n", "        width, height = 2*k*np.sqrt(vals)\n", "        ell = Ellipse(xy=mu, width=width, height=height, angle=theta, edgecolor=col, facecolor='none', lw=2, alpha=0.7)\n", "        ax.add_patch(ell)\n", "    ax.set_xlabel(pair[0]); ax.set_ylabel(pair[1]); ax.set_title('Elipses de confiança bivariadas (aprox)')\n", "    plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'scatter_ellipses.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "9c4d53b9", "metadata": {}, "outputs": [], "source": ["#@title Resíduos padronizados e marcação de >2-3σ\n", "from sklearn.linear_model import LinearRegression\n", "nums = [c for c in df.select_dtypes(include=['number']).columns]\n", "if len(nums)>=2:\n", "    X = pd.to_numeric(df[nums[0]], errors='coerce').values.reshape(-1,1)\n", "    y = pd.to_numeric(df[nums[1]], errors='coerce').values\n", "    mask = ~np.isnan(X).ravel() & ~np.isnan(y)\n", "    X, y = X[mask], y[mask]\n", "    if len(y) > 10:\n", "        lr = LinearRegression().fit(X, y)\n", "        res = y - lr.predict(X)\n", "        s_res = (res - res.mean())/(res.std(ddof=1) if res.std(ddof=1)>0 else 1.0)\n", "        fig, ax = plt.subplots(1,2, figsize=(11,4))\n", "        ax[0].scatter(X, res, s=8, alpha=0.4); ax[0].axhline(0, color='k', lw=1)\n", "        for k,col in [(2,'orange'),(3,'red')]: ax[0].axhline(k*res.std(ddof=1), color=col, ls='--'); ax[0].axhline(-k*res.std(ddof=1), color=col, ls='--')\n", "        ax[0].set_title('Resíduos com bandas ±2σ/±3σ')\n", "        ax[1].hist(s_res, bins='auto', density=True, alpha=0.6); xs = np.linspace(s_res.min(), s_res.max(), 200); ax[1].plot(xs, stats.norm.pdf(xs, 0, 1), 'r--'); ax[1].set_title('Resíduos padronizados vs. N(0,1)')\n", "        plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'residuos_padronizados.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "8d3d9d9f", "metadata": {}, "outputs": [], "source": ["#@title Filtragem progressiva e impacto nas estatísticas\n", "import numpy as np\n", "cols = num_cols_n if 'num_cols_n' in globals() else FEAT_NUM[:6]\n", "summary = []\n", "for c in cols:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if len(s) < 10: continue\n", "    mu, sd = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "    q1, q3 = s.quantile(0.25), s.quantile(0.75); iqr = q3 - q1\n", "    cut_sd2 = s[(s >= mu-2*sd) & (s <= mu+2*sd)]\n", "    cut_sd3 = s[(s >= mu-3*sd) & (s <= mu+3*sd)]\n", "    cut_iqr = s[(s >= q1-1.5*iqr) & (s <= q3*****iqr)]\n", "    fig, axes = plt.subplots(1,2, figsize=(11,4))\n", "    sns.kdeplot(s, ax=axes[0], label='Bruto', warn_singular=False); sns.kdeplot(cut_iqr, ax=axes[0], label='±1.5 IQR', warn_singular=False); sns.kdeplot(cut_sd2, ax=axes[0], label='±2σ', warn_singular=False); sns.kdeplot(cut_sd3, ax=axes[0], label='±3σ', warn_singular=False)\n", "    axes[0].legend(); axes[0].set_title(f'{c}: densidades (bruto vs filtros)')\n", "    axes[1].boxplot([s, cut_iqr, cut_sd2, cut_sd3], labels=['bruto','±1.5IQR','±2σ','±3σ'], vert=False)\n", "    axes[1].set_title(f'{c}: boxplots comparativos')\n", "    plt.tight_layout(); plt.savefig(OUTLIERS_DIR / f'filters_compare_{c}.png', bbox_inches='tight'); plt.show()\n", "    summary.append({'col': c, 'n_bruto': len(s), 'n_iqr': len(cut_iqr), 'n_sd2': len(cut_sd2), 'n_sd3': len(cut_sd3)})\n", "tbl = pd.DataFrame(summary)\n", "tbl['pct_remov_iqr'] = 1 - tbl['n_iqr']/tbl['n_bruto']\n", "tbl['pct_remov_sd2'] = 1 - tbl['n_sd2']/tbl['n_bruto']\n", "tbl['pct_remov_sd3'] = 1 - tbl['n_sd3']/tbl['n_bruto']\n", "display(tbl)\n", "tbl_path = TABLES_DIR / 'outlier_filtering_impact.csv'\n", "tbl.to_csv(tbl_path, index=False)\n", "print('Salvo:', tbl_path)\n"]}, {"cell_type": "markdown", "id": "984d271a", "metadata": {}, "source": ["##### Observações para dataset limitado\n", "\n", "- Prefira limiares robustos (MAD/percentis) quando a normalidade é fraca.\n", "- Use bootstrap para ICs de estatísticas e avalie sensibilidade a n.\n", "- Documente trade-offs entre remover outliers e perda de informação.\n"]}, {"cell_type": "markdown", "id": "1936f90c", "metadata": {}, "source": ["#### 1.3.3 Bootstrap de ICs (média e mediana)\n", "\n", "- 2000 reamostragens por variável (ajustável)\n", "- ICs 95% por percentis (2.5%, 97.5%)\n", "- Comparação com ICs paramétricos assumindo normalidade\n"]}, {"cell_type": "code", "execution_count": null, "id": "a65a8c99", "metadata": {}, "outputs": [], "source": ["#@title Bootstrap de ICs (média e mediana) + comparação paramétrica\n", "import numpy as np, math, json\n", "from scipy.stats import norm\n", "# Reduzido para evitar estouro de memória em subconjuntos/ambiente regional\n", "N_BOOT = 400\n", "cols = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>=5]\n", "rows = []\n", "for c in cols:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna().values\n", "    n = len(s)\n", "    if n < 20:\n", "        continue\n", "    # Bootstrap\n", "    rng = np.random.default_rng(42)\n", "    idx = rng.integers(0, n, size=(N_BOOT, n))\n", "    samples = s[idx]\n", "    boot_means = samples.mean(axis=1)\n", "    boot_medians = np.median(samples, axis=1)\n", "    m, md = float(s.mean()), float(np.median(s))\n", "    sd = float(s.std(ddof=1)) if s.std(ddof=1)>0 else 1.0\n", "    se_mean = sd/math.sqrt(n)\n", "    z = 1.96\n", "    # Paramétrico: média\n", "    mean_low_p, mean_high_p = m - z*se_mean, m + z*se_mean\n", "    # Paramétrico (aprox) para mediana: sd_median ≈ 1.253*sd/sqrt(n)\n", "    se_median = 1.253*sd/math.sqrt(n)\n", "    med_low_p, med_high_p = md - z*se_median, md + z*se_median\n", "    # Bootstrap percentis\n", "    mean_low_b, mean_high_b = np.percentile(boot_means, [2.5, 97.5])\n", "    med_low_b, med_high_b = np.percentile(boot_medians, [2.5, 97.5])\n", "    rows.append({\n", "        'col': c, 'n': n, 'mean': m, 'mean_ci_boot_low': mean_low_b, 'mean_ci_boot_high': mean_high_b,\n", "        'mean_ci_param_low': mean_low_p, 'mean_ci_param_high': mean_high_p,\n", "        'median': md, 'median_ci_boot_low': med_low_b, 'median_ci_boot_high': med_high_b,\n", "        'median_ci_param_low': med_low_p, 'median_ci_param_high': med_high_p,\n", "        'n_boot': N_BOOT\n", "    })\n", "boot_tbl = pd.DataFrame(rows)\n", "display(boot_tbl.head())\n", "boot_path = TABLES_DIR / 'bootstrap_confidence_intervals.csv'\n", "boot_tbl.to_csv(boot_path, index=False)\n", "print('Salvo:', boot_path)\n"]}, {"cell_type": "code", "execution_count": null, "id": "ba83b9aa", "metadata": {}, "outputs": [], "source": ["#@title Visualização dos ICs (barras de erro)\n", "if 'boot_tbl' in globals() and not boot_tbl.empty:\n", "    top = boot_tbl.sort_values('n', ascending=False).head(8)\n", "    # Médias\n", "    plt.figure(figsize=(10,5))\n", "    x = np.arange(len(top))\n", "    y = top['mean'].values\n", "    yerr = np.vstack([y - top['mean_ci_boot_low'].values, top['mean_ci_boot_high'].values - y])\n", "    plt.errorbar(x, y, yerr=yerr, fmt='o', capsize=4, label='IC bootstrap')\n", "    yerr_p = np.vstack([y - top['mean_ci_param_low'].values, top['mean_ci_param_high'].values - y])\n", "    plt.errorbar(x, y, yerr=yerr_p, fmt='s', capsize=4, label='IC paramétrico', alpha=0.7)\n", "    plt.xticks(x, top['col'].tolist(), rotation=45)\n", "    plt.title('ICs 95% da média (bootstrap vs paramétrico)')\n", "    plt.legend(); plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'bootstrap_ics_means.png', bbox_inches='tight'); plt.show()\n", "    # Medianas\n", "    plt.figure(figsize=(10,5))\n", "    y2 = top['median'].values\n", "    yerr2 = np.vstack([y2 - top['median_ci_boot_low'].values, top['median_ci_boot_high'].values - y2])\n", "    plt.errorbar(x, y2, yerr=yerr2, fmt='o', capsize=4, label='IC bootstrap')\n", "    yerr2p = np.vstack([y2 - top['median_ci_param_low'].values, top['median_ci_param_high'].values - y2])\n", "    plt.errorbar(x, y2, yerr=yerr2p, fmt='s', capsize=4, label='IC paramétrico (aprox)', alpha=0.7)\n", "    plt.xticks(x, top['col'].tolist(), rotation=45)\n", "    plt.title('ICs 95% da mediana (bootstrap vs paramétrico aprox)')\n", "    plt.legend(); plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'bootstrap_ics_medians.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "markdown", "id": "91ddcce5", "metadata": {}, "source": ["#### 1.3.4 Sumário Orientativo e Configuração para Pipeline\n", "\n", "- Recomendações por variável (sigma vs MAD/IQR) a partir de Shapiro p\n", "- Geração de config JSON reutilizável\n", "- Visualização de % outliers por método e concordância entre métodos\n"]}, {"cell_type": "code", "execution_count": null, "id": "25e59094", "metadata": {}, "outputs": [], "source": ["#@title Recomendações por normalidade (Shapiro) e config para pipeline\n", "import json, numpy as np\n", "# Reusar resultados de normalidade se existirem; caso con<PERSON><PERSON><PERSON>, calcular <PERSON> m<PERSON>\n", "if 'stats_tbl' in globals() and not stats_tbl.empty:\n", "    norm_df = stats_tbl[['col','shapiro_p']].copy()\n", "else:\n", "    cols = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>=5]\n", "    recs = []\n", "    for c in cols:\n", "        s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "        if len(s) >= 8:\n", "            w_stat, w_p = stats.shapiro(s.sample(min(5000, len(s)), random_state=42))\n", "            recs.append({'col': c, 'shapiro_p': float(w_p)})\n", "    norm_df = pd.DataFrame(recs)\n", "# Regras\n", "def recommend(p):\n", "    return 'sigma' if (p is not None and p > 0.05) else 'robust'\n", "norm_df['method'] = norm_df['shapiro_p'].apply(recommend)\n", "# <PERSON><PERSON> recomendado\n", "cfg_rows = []\n", "for _, r in norm_df.iterrows():\n", "    c = r['col']; method = r['method']; p = r['shapiro_p']\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    if method == 'sigma':\n", "        mu, sd = float(s.mean()), float(s.std(ddof=1) if s.std(ddof=1)>0 else 1.0)\n", "        lim = {'low': mu - 3*sd, 'high': mu + 3*sd}\n", "        just = f'p={p:.3f} > 0.05; usar ±3σ (assumindo normalidade aproximada)'\n", "    else:\n", "        q1, q3 = float(s.quantile(0.25)), float(s.quantile(0.75)); iqr = q3 - q1\n", "        low, high = q1 - 1.5*iqr, q3 + 1.5*iqr\n", "        just = f'p={p:.3f} ≤ 0.05; usar IQR (robusto)'; lim = {'low': low, 'high': high}\n", "    cfg_rows.append({'col': c, 'method': ('sigma' if method=='sigma' else 'iqr'), 'threshold': lim, 'justification': just})\n", "rec_tbl = pd.DataFrame(cfg_rows)\n", "display(rec_tbl)\n", "rec_path = TABLES_DIR / 'outlier_method_recommendations.csv'\n", "rec_tbl.to_csv(rec_path, index=False)\n", "print('Salvo:', rec_path)\n", "# JSON de configuração\n", "cfg = {row['col']: {'method': row['method'], 'threshold': row['threshold'], 'justification': row['justification']} for _, row in rec_tbl.iterrows()}\n", "cfg_path = TABLES_DIR / 'preprocessing_config.json'\n", "with open(cfg_path, 'w', encoding='utf-8') as f:\n", "    json.dump(cfg, f, ensure_ascii=False, indent=2)\n", "print('Salvo:', cfg_path)\n"]}, {"cell_type": "code", "execution_count": null, "id": "0fff0c29", "metadata": {}, "outputs": [], "source": ["#@title Visualização consolidada: % outliers por método e concordância\n", "import numpy as np\n", "cols = [c for c in df.select_dtypes(include=['number']).columns if df[c].nunique()>=5][:10]\n", "summary = []\n", "agree_rows = []\n", "for c in cols:\n", "    s = pd.to_numeric(df[c], errors='coerce').dropna()\n", "    n = len(s)\n", "    if n < 20: continue\n", "    # sigma\n", "    mu, sd = s.mean(), s.std(ddof=1) if s.std(ddof=1)>0 else 1.0\n", "    mask2 = (s < mu-2*sd) | (s > mu+2*sd)\n", "    mask3 = (s < mu-3*sd) | (s > mu+3*sd)\n", "    # iqr\n", "    q1, q3 = s.quantile(0.25), s.quantile(0.75); iqr = q3 - q1\n", "    mask_iqr = (s < q1-1.5*iqr) | (s > q3*****iqr)\n", "    summary.append({'col': c, '%_2sigma': mask2.mean(), '%_3sigma': mask3.mean(), '%_iqr': mask_iqr.mean()})\n", "    # concord<PERSON><PERSON> (<PERSON><PERSON><PERSON>) entre métodos\n", "    def jacc(a, b):\n", "        inter = np.logical_and(a, b).sum()\n", "        uni = np.logical_or(a, b).sum()\n", "        return inter/uni if uni>0 else 1.0\n", "    agree_rows.append({'col': c, 'J(2σ,3σ)': jacc(mask2, mask3), 'J(2σ,IQR)': jacc(mask2, mask_iqr), 'J(3σ,IQR)': jacc(mask3, mask_iqr)})\n", "sum_tbl = pd.DataFrame(summary)\n", "display(sum_tbl)\n", "sum_path = TABLES_DIR / 'outlier_detection_rates.csv'\n", "sum_tbl.to_csv(sum_path, index=False)\n", "print('Salvo:', sum_path)\n", "agree_tbl = pd.DataFrame(agree_rows).set_index('col')\n", "plt.figure(figsize=(6,4))\n", "sns.heatmap(agree_tbl, annot=True, vmin=0, vmax=1, cmap='YlGnBu')\n", "plt.title('<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) entre métodos de outlier')\n", "plt.tight_layout(); plt.savefig(OUTLIERS_DIR / 'outlier_methods_agreement.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "markdown", "id": "21030547", "metadata": {}, "source": ["### 1.4 Correlações Parciais (controle de confundidores)\n", "\n", "- Controle por 'qtd' quando disponível (correlação de Pearson nos resíduos)\n"]}, {"cell_type": "code", "execution_count": null, "id": "70ea0903", "metadata": {}, "outputs": [], "source": ["#@title Correlações parciais controlando por 'qtd' (se existir)\n", "import numpy as np\n", "conf = 'qtd' if 'qtd' in df.columns else None\n", "num_cols3 = [c for c in FEAT_NUM if df[c].nunique()>5]\n", "if conf and conf in num_cols3:\n", "    cols = [c for c in num_cols3 if c != conf][:10]\n", "    M = df[[conf]].fillna(0).values\n", "    def resid(y, X):\n", "        X1 = np.hstack([np.ones((len(X),1)), X])\n", "        beta, *_ = np.linalg.lstsq(X1, y, rcond=None)\n", "        return y - X1@beta\n", "    R = np.zeros((len(cols), len(cols)))\n", "    for i, ci in enumerate(cols):\n", "        yi = pd.to_numeric(df[ci], errors='coerce').fillna(0).values.reshape(-1,1)\n", "        ri = resid(yi, M).ravel()\n", "        for j, cj in enumerate(cols):\n", "            yj = pd.to_numeric(df[cj], errors='coerce').fillna(0).values.reshape(-1,1)\n", "            rj = resid(yj, M).ravel()\n", "            if i==j:\n", "                R[i,j] = 1.0\n", "            else:\n", "                R[i,j] = np.corrcoef(ri, rj)[0,1]\n", "    pcorr = pd.DataFrame(R, index=cols, columns=cols)\n", "    plt.figure(figsize=(min(12, 2+0.5*len(cols)), min(10, 2+0.5*len(cols))))\n", "    sns.heatmap(pcorr, cmap='vlag', center=0)\n", "    plt.title(\"Correlação Parcial (controle 'qtd')\")\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / 'partial_corr_control_qtd.png', bbox_inches='tight'); plt.show()\n", "else:\n", "    print('Sem variável de controle numérica adequada (ex.: qtd).')\n"]}, {"cell_type": "markdown", "id": "eb8dd186", "metadata": {}, "source": ["### 1.5 Visualizações Comparativas por Grupos\n", "\n", "- Comparações de distribuições entre segmentos relevantes\n"]}, {"cell_type": "code", "execution_count": null, "id": "617091ba", "metadata": {}, "outputs": [], "source": ["#@title ECDF/KDE comparando grupos\n", "cat_pref = 'Tipo_PDV' if 'Tipo_PDV' in df.columns else ('uf' if 'uf' in df.columns else None)\n", "metric = 'valor' if 'valor' in df.columns else (num_cols3[0] if num_cols3 else None)\n", "if cat_pref and metric:\n", "    vals = df[cat_pref].value_counts().head(4).index.tolist()\n", "    sub = df[df[cat_pref].isin(vals)].copy()\n", "    plt.figure(figsize=(8,5))\n", "    for v in vals:\n", "        s = pd.to_numeric(sub.loc[sub[cat_pref]==v, metric], errors='coerce').dropna()\n", "        sns.ecdfplot(s, label=str(v))\n", "    plt.legend(); plt.title(f'ECDF de {metric} por {cat_pref}')\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / f'ecdf_{metric}_by_{cat_pref}.png', bbox_inches='tight'); plt.show()\n", "    plt.figure(figsize=(8,5))\n", "    for v in vals:\n", "        s = pd.to_numeric(sub.loc[sub[cat_pref]==v, metric], errors='coerce').dropna()\n", "        sns.kdeplot(s, label=str(v), fill=False, warn_singular=False)\n", "    plt.legend(); plt.title(f'KDE de {metric} por {cat_pref}')\n", "    plt.tight_layout(); plt.savefig(EDA_PLOTS_DIR / f'kde_{metric}_by_{cat_pref}.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "markdown", "id": "b69c7035", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "- Nulos; Outliers (MAD) + winsorização; Codificação (OneHot); Normalização (StandardScaler)"]}, {"cell_type": "code", "execution_count": null, "id": "66feabce", "metadata": {}, "outputs": [], "source": ["#@title Limpeza de nulos e preparação\n", "df_pp = df.copy()\n", "if 'qtd' in df_pp.columns:\n", "    df_pp['qtd'] = df_pp['qtd'].fillna(0)\n", "df_pp = df_pp.dropna(subset=['data','id_loja'])\n", "df_pp.shape\n"]}, {"cell_type": "code", "execution_count": null, "id": "9fd7d07d", "metadata": {}, "outputs": [], "source": ["#@title Outliers (MAD) e winsorização\n", "import numpy as np\n", "def robust_z(x):\n", "    med = x.median(); mad = (x-med).abs().median()\n", "    if mad == 0: return pd.Series(np.zeros(len(x)), index=x.index)\n", "    return 0.6745*(x - med)/mad\n", "df_pp['rz'] = df_pp.groupby('id_loja')['valor'].transform(robust_z)\n", "outliers = df_pp[df_pp['rz'].abs() >= 3]\n", "cap_low = df_pp['valor'].quantile(0.01)\n", "cap_high = df_pp['valor'].quantile(0.99)\n", "df_pp['valor_winsor'] = df_pp['valor'].clip(lower=cap_low, upper=cap_high)\n", "df_pp[['valor','valor_winsor']].describe().T\n"]}, {"cell_type": "code", "execution_count": null, "id": "4c3fa39c", "metadata": {}, "outputs": [], "source": ["#@title Codificação e normalização\n", "from sklearn.preprocessing import OneHotEncoder, StandardScaler\n", "from scipy.sparse import csr_matrix, hstack as sparse_hstack\n", "cat_cols = [c for c in ['uf','cidade','Tipo_PDV'] if c in df_pp.columns]\n", "num_cols = [c for c in ['valor_winsor','qtd'] if c in df_pp.columns]\n", "# Saída esparsa para evitar MemoryError\n", "ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=True)\n", "X_cat = ohe.fit_transform(df_pp[cat_cols]) if cat_cols else csr_matrix((len(df_pp),0))\n", "scaler = StandardScaler()\n", "X_num = scaler.fit_transform(df_pp[num_cols]) if num_cols else np.empty((len(df_pp),0))\n", "X_num_sp = csr_matrix(X_num) if num_cols else csr_matrix((len(df_pp),0))\n", "X = sparse_hstack([X_num_sp, X_cat], format='csr'); X.shape\n"]}, {"cell_type": "markdown", "id": "43651f07", "metadata": {}, "source": ["## 3. <PERSON><PERSON><PERSON><PERSON> (H1–H3)\n", "H1: SP > demais <PERSON> (receita por loja)\n", "\n", "H2: <PERSON><PERSON> de semana ≠ dias ú<PERSON> (receita diária)\n", "\n", "H3: Cidades com múltiplas lojas > lojas únicas (total e por loja)"]}, {"cell_type": "code", "execution_count": null, "id": "1e3c2e31", "metadata": {}, "outputs": [], "source": ["#@title Testes H1–H3 (<PERSON><PERSON>)\n", "from scipy import stats\n", "# H1\n", "sp = df.groupby(['uf','id_loja'])['valor'].sum().reset_index()\n", "sp['valor_por_loja'] = sp['valor']\n", "sp_vals = sp.loc[sp['uf']=='SP','valor_por_loja']\n", "others = sp.loc[sp['uf']!='SP','valor_por_loja']\n", "if len(sp_vals)>0 and len(others)>0:\n", "    stat1, p1 = stats.mannwhitneyu(sp_vals, others, alternative='greater')\n", "    print(f'H1 - <PERSON><PERSON><PERSON> (SP > outras UFs): U={stat1:.2f} p={p1:.4g}')\n", "# H2\n", "df['is_weekend'] = df['data'].dt.dayofweek >= 5\n", "dw_daily = df.groupby(['data','is_weekend'])['valor'].sum().reset_index()\n", "weekend = dw_daily.loc[dw_daily['is_weekend']==True,'valor']\n", "weekday = dw_daily.loc[dw_daily['is_weekend']==False,'valor']\n", "if len(weekend)>0 and len(weekday)>0:\n", "    stat2, p2 = stats.mannwhitneyu(weekend, weekday, alternative='two-sided')\n", "    print(f'H2 - <PERSON><PERSON><PERSON> (fim de semana != dias <PERSON><PERSON><PERSON>): U={stat2:.2f} p={p2:.4g}')\n", "# H3\n", "city_store = df.groupby(['uf','cidade'])['id_loja'].nunique().reset_index(name='n_lojas')\n", "city_rev = df.groupby(['uf','cidade'])['valor'].sum().reset_index(name='receita_total')\n", "city = city_store.merge(city_rev, on=['uf','cidade'])\n", "city['receita_por_loja'] = city['receita_total']/city['n_lojas']\n", "multi = city.loc[city['n_lojas']>1]\n", "single = city.loc[city['n_lojas']==1]\n", "if len(multi)>0 and len(single)>0:\n", "    s3a, p3a = stats.mannwhitneyu(multi['receita_total'], single['receita_total'], alternative='greater')\n", "    s3b, p3b = stats.mannwhitneyu(multi['receita_por_loja'], single['receita_por_loja'], alternative='greater')\n", "    print(f'H3 - total (multi>single): U={s3a:.2f} p={p3a:.4g}')\n", "    print(f'H3 - por loja (multi>single): U={s3b:.2f} p={p3b:.4g}')\n"]}, {"cell_type": "code", "execution_count": null, "id": "6231492e", "metadata": {}, "outputs": [], "source": ["#@title Diagnóstico de distribuição e tamanho de efeito (delta de Cliff)\n", "import scipy.stats as ss\n", "try:\n", "    import statsmodels.api as sm\n", "except Exception:\n", "    sm = None\n", "def cliffs_delta(x, y):\n", "    x = pd.Series(x).dropna().values; y = pd.Series(y).dropna().values\n", "    gt = sum(xx > yy for xx in x for yy in y)\n", "    lt = sum(xx < yy for xx in x for yy in y)\n", "    n = len(x)*len(y)\n", "    return (gt - lt)/n if n>0 else float('nan')\n", "samples = {\n", "    'SP por loja': sp_vals,\n", "    'Outras UFs por loja': others,\n", "    'Re<PERSON>ita diária fim de semana': weekend,\n", "    'Re<PERSON>ita diária dia <PERSON>': weekday\n", "}\n", "for name, s in samples.items():\n", "    s = pd.Series(s).dropna()\n", "    if len(s) > 8:\n", "        stat, p = ss.shapiro(s.sample(min(5000, len(s)), random_state=42))\n", "        print(f'{name}: <PERSON><PERSON><PERSON><PERSON><PERSON> p={p:.4g}')\n", "        \n", "        if sm is not None:\n", "            sm.qqplot(s, line='s'); plt.title(f'QQ-plot: {name}'); plt.show()\n", "        else:\n", "            print('statsmodels ausente; pulando QQ-plot.')\n", "# Cliff's delta\n", "if len(sp_vals)>0 and len(others)>0:\n", "    print(f\"H1 - Cliff's delta: {cliffs_delta(sp_vals, others):.3f}\")\n", "if len(weekend)>0 and len(weekday)>0:\n", "    print(f\"H2 - Cliff's delta: {cliffs_delta(weekend, weekday):.3f}\")\n", "if len(multi)>0 and len(single)>0:\n", "    print(f\"H3 - Cliff's delta (total): {cliffs_delta(multi['receita_total'], single['receita_total']):.3f}\")\n", "    print(f\"H3 - Cliff's delta (por loja): {cliffs_delta(multi['receita_por_loja'], single['receita_por_loja']):.3f}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "e86b8f2c", "metadata": {}, "outputs": [], "source": ["#@title <PERSON><PERSON><PERSON> (p-valores ajustados)\n", "import numpy as np\n", "pvals = []\n", "labels = []\n", "if 'p1' in globals(): pvals.append(p1); labels.append('H1')\n", "if 'p2' in globals(): pvals.append(p2); labels.append('H2')\n", "if 'p3a' in globals() and 'p3b' in globals(): pvals.append(max(p3a, p3b)); labels.append('H3 (maior p)')\n", "def holm_bonferroni(ps):\n", "    m = len(ps); order = np.argsort(ps); adj = np.empty(m)\n", "    for i, idx in enumerate(order):\n", "        adj[idx] = min((m - i) * ps[idx], 1.0)\n", "    return adj\n", "if pvals:\n", "    adj = holm_bonferroni(pvals)\n", "    for lab, raw, ap in zip(labels, pvals, adj):\n", "        print(f'{lab}: bruto={raw:.4g} | ajustado(Holm)={ap:.4g}')\n"]}, {"cell_type": "markdown", "id": "6ec5e389", "metadata": {}, "source": ["## 4. Integração com Resultados Existentes\n", "- Referência aos artefatos em `reports/2025-08-15/` (16 PNGs, 18 CSVs)"]}, {"cell_type": "code", "execution_count": null, "id": "ce6738f6", "metadata": {}, "outputs": [], "source": ["#@title Leitura de tabelas geradas (exemplos)\n", "import os\n", "tables = sorted([p for p in os.listdir(TABLES_DIR) if p.endswith('.csv')])[:5]\n", "print('Exemplos CSV:', tables)\n", "if (TABLES_DIR / 'summary.csv').exists():\n", "    example = pd.read_csv(TABLES_DIR / 'summary.csv')\n", "    display(example.head())\n"]}, {"cell_type": "markdown", "id": "d11e70ee", "metadata": {}, "source": ["## 5. Validação automática e checklist final\n", "Valida PNG=16 e CSV=18 e lista diretórios vazios."]}, {"cell_type": "code", "execution_count": null, "id": "b925f9c0", "metadata": {}, "outputs": [], "source": ["#@title Validação automática dos artefatos e diretórios vazios\n", "import os\n", "pngs = [p for p in os.listdir(PLOTS_DIR) if p.endswith('.png')]\n", "csvs = [p for p in os.listdir(TABLES_DIR) if p.endswith('.csv')]\n", "print('PNG files:', len(pngs), ' (esperado: 16)')\n", "print('CSV files:', len(csvs), ' (esperado: 18)')\n", "empty_dirs = []\n", "for root, dirs, files in os.walk('.'):\n", "    for d in dirs:\n", "        full = Path(root) / d\n", "        try:\n", "            if len(list((full).iterdir())) == 0:\n", "                empty_dirs.append(str(full))\n", "        except Exception:\n", "            pass\n", "print('<PERSON><PERSON><PERSON><PERSON><PERSON> vazios:', empty_dirs if empty_dirs else 'nenhum')\n"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}