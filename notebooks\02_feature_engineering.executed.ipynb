{"cells": [{"cell_type": "markdown", "id": "81e09975", "metadata": {}, "source": ["# Feature Engineering - <PERSON><PERSON>s Dataset\n", "\n", "Notebook dedicado ao preparo de variáveis para modelagem, fundamentado nos resultados da Parte 1 (EDA)."]}, {"cell_type": "markdown", "id": "743ae75a", "metadata": {}, "source": ["## 1. Setup e Importação de Resultados da EDA"]}, {"cell_type": "code", "execution_count": 1, "id": "4129a1aa", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:44.959421Z", "iopub.status.busy": "2025-09-11T21:09:44.959421Z", "iopub.status.idle": "2025-09-11T21:09:47.190723Z", "shell.execute_reply": "2025-09-11T21:09:47.189738Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DATA_CLEAN: ..\\data\\clean\\cleaned_featured.csv\n"]}], "source": ["# Paths e imports\n", "from pathlib import Path\n", "import pandas as pd, numpy as np, json, math\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from warnings import filterwarnings; filterwarnings('ignore')\n", "BASE_DIR = Path('.')\n", "if not (BASE_DIR / 'data' / 'clean' / 'cleaned_featured.csv').exists(): BASE_DIR = Path('..')\n", "DATA_CLEAN = BASE_DIR / 'data' / 'clean' / 'cleaned_featured.csv'\n", "PROC_DIR = BASE_DIR / 'data' / 'processed'\n", "REPORTS_DIR = BASE_DIR / 'reports' / '2025-08-15'\n", "TABLES_DIR = REPORTS_DIR / 'tables'\n", "PLOTS_FE_DIR = REPORTS_DIR / 'plots' / 'feature_engineering'\n", "for d in [PROC_DIR, TABLES_DIR, PLOTS_FE_DIR]: d.mkdir(parents=True, exist_ok=True)\n", "CFG_JSON = TABLES_DIR / 'preprocessing_config.json'\n", "NORM_CSV = TABLES_DIR / 'normality_tests.csv'\n", "REC_CSV = TABLES_DIR / 'outlier_method_recommendations.csv'\n", "print('DATA_CLEAN:', DATA_CLEAN)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "51293c84", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:47.194744Z", "iopub.status.busy": "2025-09-11T21:09:47.194186Z", "iopub.status.idle": "2025-09-11T21:09:47.737087Z", "shell.execute_reply": "2025-09-11T21:09:47.737087Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["cfg vars: 6 | norm_tbl: (6, 5) | rec_tbl: (6, 4)\n"]}], "source": ["# Carregar dados e artefatos da EDA\n", "df = pd.read_csv(DATA_CLEAN, parse_dates=['data'])\n", "cfg = json.loads(CFG_JSON.read_text(encoding='utf-8')) if CFG_JSON.exists() else {}\n", "norm_tbl = pd.read_csv(NORM_CSV) if NORM_CSV.exists() else pd.DataFrame()\n", "rec_tbl = pd.read_csv(REC_CSV) if REC_CSV.exists() else pd.DataFrame()\n", "print('cfg vars:', len(cfg), '| norm_tbl:', norm_tbl.shape, '| rec_tbl:', rec_tbl.shape)\n"]}, {"cell_type": "markdown", "id": "c5729591", "metadata": {}, "source": ["## 2. Seleção Fundamentada de Variáveis"]}, {"cell_type": "code", "execution_count": 3, "id": "94b75f80", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:47.741146Z", "iopub.status.busy": "2025-09-11T21:09:47.741146Z", "iopub.status.idle": "2025-09-11T21:09:48.000526Z", "shell.execute_reply": "2025-09-11T21:09:48.000526Z"}}, "outputs": [{"data": {"text/plain": ["{'n_num_orig': 29,\n", " 'n_id_like': 9,\n", " 'n_num_sel': 14,\n", " 'exclusions':              reason                  col\n", " 0      low_variance                month\n", " 1      low_variance              quarter\n", " 2  high_correlation  Total_Preco_Liquido\n", " 3  high_correlation   Total_Preco_Varejo\n", " 4  high_correlation         Preco_Varejo\n", " 5  high_correlation        valor_per_qtd}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Heurísticas: remover constantes/baixa variância; multicolinearidade alta;\n", "num_cols = df.select_dtypes(include=['number']).columns.tolist()\n", "# Remover IDs explícitos da etapa de modelagem (mantê-los apenas para joins/contagens)\n", "id_like = [c for c in num_cols if str(c).lower().replace(' ','') in ['id','id_loja','id_produto','id_cliente','id_vendedor','id_faturamento','documento','transacao'] or str(c).lower().startswith('id_') or str(c).lower().endswith('_id')]\n", "num_cols = [c for c in num_cols if c not in id_like]\n", "cat_cols = [c for c in df.select_dtypes(exclude=['number']).columns if c not in ['data']]\n", "# 2.1 Remover constantes e quase-constantes\n", "low_var = [c for c in num_cols if df[c].nunique()<=1]\n", "num_cols = [c for c in num_cols if c not in low_var]\n", "# 2.2 Correlação alta (|r|>0.95) => eliminar uma das variáveis\n", "corr = df[num_cols].corr(method='spearman').abs() if len(num_cols)>1 else pd.DataFrame()\n", "to_drop = set()\n", "if not corr.empty:\n", "    upper = corr.where(np.triu(np.ones(corr.shape), k=1).astype(bool))\n", "    for c in upper.columns:\n", "        if any(upper[c] > 0.95): to_drop.add(c)\n", "num_sel = [c for c in num_cols if c not in to_drop]\n", "# 2.3 Documentar exclusões\n", "exclusions = pd.concat([\n", "    pd.DataFrame({'reason': 'low_variance', 'col': low_var}),\n", "    pd.DataFrame({'reason': 'high_correlation', 'col': list(to_drop)})\n", "], ignore_index=True)\n", "display({'n_num_orig': len(df.select_dtypes(include=['number']).columns), 'n_id_like': len(id_like), 'n_num_sel': len(num_sel), 'exclusions': exclusions})\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a4e6b9fe", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:48.005418Z", "iopub.status.busy": "2025-09-11T21:09:48.004440Z", "iopub.status.idle": "2025-09-11T21:09:48.065391Z", "shell.execute_reply": "2025-09-11T21:09:48.064405Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>qtd</th>\n", "      <th>Preco_Custo</th>\n", "      <th>valor</th>\n", "      <th>Frete</th>\n", "      <th>Num_Vale</th>\n", "      <th>Desconto</th>\n", "      <th>DESCONTO_CALCULADO</th>\n", "      <th>Dim_<PERSON><PERSON>.Cod_Franqueado</th>\n", "      <th>dow</th>\n", "      <th>day</th>\n", "      <th>...</th>\n", "      <th>Dim_Produtos.Segmentacao</th>\n", "      <th>Dim_Produtos.Shape</th>\n", "      <th>Dim_Produtos.Formato</th>\n", "      <th>Dim_Produtos.Sexo</th>\n", "      <th>Dim_Produtos.Griffe</th>\n", "      <th>Dim_Produtos.GRUPO_CHILLI</th>\n", "      <th>is_weekend</th>\n", "      <th>store_first_date</th>\n", "      <th>store_perf_terc</th>\n", "      <th>data</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>7.00</td>\n", "      <td>2097</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>51.78</td>\n", "      <td>0.832</td>\n", "      <td>401325.0</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>ACESSORIOS</td>\n", "      <td>UNISSEX</td>\n", "      <td>CHILLI BEANS</td>\n", "      <td>ACESSORIOS</td>\n", "      <td>True</td>\n", "      <td>2025-03-01</td>\n", "      <td>mid</td>\n", "      <td>2025-03-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>82.34</td>\n", "      <td>28560</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>223.05</td>\n", "      <td>114.380</td>\n", "      <td>400013.0</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>CASUAL</td>\n", "      <td>QUADRADO</td>\n", "      <td>QUADRADO</td>\n", "      <td>MASCULINO</td>\n", "      <td>CHILLI BEANS</td>\n", "      <td>OCULOS</td>\n", "      <td>True</td>\n", "      <td>2025-03-01</td>\n", "      <td>low</td>\n", "      <td>2025-03-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>228.16</td>\n", "      <td>34999</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0.000</td>\n", "      <td>401823.0</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>CASUAL</td>\n", "      <td>QUADRADO</td>\n", "      <td>QUADRADO</td>\n", "      <td>FEMININO</td>\n", "      <td>PREMIUM</td>\n", "      <td>OCULOS</td>\n", "      <td>True</td>\n", "      <td>2025-03-01</td>\n", "      <td>high</td>\n", "      <td>2025-03-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>105.36</td>\n", "      <td>29998</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0.000</td>\n", "      <td>401983.0</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>CASUAL</td>\n", "      <td>REDONDO</td>\n", "      <td>REDONDO</td>\n", "      <td>UNISSEX</td>\n", "      <td>CHILLI BEANS</td>\n", "      <td>OCULOS</td>\n", "      <td>True</td>\n", "      <td>2025-03-01</td>\n", "      <td>low</td>\n", "      <td>2025-03-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1</td>\n", "      <td>105.36</td>\n", "      <td>29998</td>\n", "      <td>0.0</td>\n", "      <td>0</td>\n", "      <td>0.00</td>\n", "      <td>0.000</td>\n", "      <td>400015.0</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>CASUAL</td>\n", "      <td>REDONDO</td>\n", "      <td>REDONDO</td>\n", "      <td>UNISSEX</td>\n", "      <td>CHILLI BEANS</td>\n", "      <td>OCULOS</td>\n", "      <td>True</td>\n", "      <td>2025-03-01</td>\n", "      <td>mid</td>\n", "      <td>2025-03-01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 60 columns</p>\n", "</div>"], "text/plain": ["   qtd  Preco_Custo  valor  Frete  Num_Vale  Desconto  DESCONTO_CALCULADO  \\\n", "0    1         7.00   2097    0.0         0     51.78               0.832   \n", "1    1        82.34  28560    0.0         0    223.05             114.380   \n", "2    1       228.16  34999    0.0         0      0.00               0.000   \n", "3    1       105.36  29998    0.0         0      0.00               0.000   \n", "4    1       105.36  29998    0.0         0      0.00               0.000   \n", "\n", "   Dim_Lojas.Cod_Franqueado  dow  day  ...  Dim_Produtos.Segmentacao  \\\n", "0                  401325.0    5    1  ...                       NaN   \n", "1                  400013.0    5    1  ...                    CASUAL   \n", "2                  401823.0    5    1  ...                    CASUAL   \n", "3                  401983.0    5    1  ...                    CASUAL   \n", "4                  400015.0    5    1  ...                    CASUAL   \n", "\n", "   Dim_Produtos.<PERSON><PERSON>pe  Dim_Produtos.Formato  Dim_Produtos.Sexo  \\\n", "0                 NaN            ACESSORIOS            UNISSEX   \n", "1            QUADRADO              QUADRADO          MASCULINO   \n", "2            QUADRADO              QUADRADO           FEMININO   \n", "3             REDONDO               REDONDO            UNISSEX   \n", "4             REDONDO               REDONDO            UNISSEX   \n", "\n", "  Dim_Produtos.Griffe Dim_Produtos.GRUPO_CHILLI is_weekend store_first_date  \\\n", "0        CHILLI BEANS                ACESSORIOS       True       2025-03-01   \n", "1        CHILLI BEANS                    OCULOS       True       2025-03-01   \n", "2             PREMIUM                    OCULOS       True       2025-03-01   \n", "3        CHILLI BEANS                    OCULOS       True       2025-03-01   \n", "4        CHILLI BEANS                    OCULOS       True       2025-03-01   \n", "\n", "  store_perf_terc       data  \n", "0             mid 2025-03-01  \n", "1             low 2025-03-01  \n", "2            high 2025-03-01  \n", "3             low 2025-03-01  \n", "4             mid 2025-03-01  \n", "\n", "[5 rows x 60 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Seleção final (numéricas + categóricas úteis)\n", "use_cols = num_sel + cat_cols + (['data'] if 'data' in df.columns else [])\n", "df_sel = df[use_cols].copy()\n", "df_sel.head()\n"]}, {"cell_type": "markdown", "id": "62d4020f", "metadata": {}, "source": ["## 3. Transformações de Distribuição"]}, {"cell_type": "code", "execution_count": 5, "id": "711a226e", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:48.068851Z", "iopub.status.busy": "2025-09-11T21:09:48.067343Z", "iopub.status.idle": "2025-09-11T21:09:48.583785Z", "shell.execute_reply": "2025-09-11T21:09:48.583785Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transf. distribuição aplicadas: 9\n"]}], "source": ["# Regras: log1p para assimetria positiva; Box-Cox quando >0 e melhora normalidade\n", "from scipy.stats import boxcox\n", "transforms = {}\n", "for c in num_sel:\n", "    s = pd.to_numeric(df_sel[c], errors='coerce')\n", "    skew = s.skew()\n", "    applied = None\n", "    if skew>1 and (s>=0).all():\n", "        df_sel[c+'_log1p'] = np.log1p(s)\n", "        applied = 'log1p'\n", "    if applied is None and (s>0).all() and s.notna().sum()>20:\n", "        try:\n", "            bc, lam = boxcox(s.dropna())\n", "            df_sel[c+'_boxcox'] = np.nan; df_sel.loc[s.notna(), c+'_boxcox'] = bc\n", "            transforms[c] = {'distribution': applied or 'boxcox', 'lambda': float(lam)}\n", "            continue\n", "        except Exception: pass\n", "    if applied: transforms[c] = {'distribution': applied}\n", "print('Transf. distribuição aplicadas:', len(transforms))\n"]}, {"cell_type": "markdown", "id": "98fb0262", "metadata": {}, "source": ["## 4. Tratamento de Outliers (baseado em config JSON)"]}, {"cell_type": "code", "execution_count": 6, "id": "0e606dd8", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:48.586724Z", "iopub.status.busy": "2025-09-11T21:09:48.586724Z", "iopub.status.idle": "2025-09-11T21:09:48.602933Z", "shell.execute_reply": "2025-09-11T21:09:48.601944Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Outliers tratados: 5\n"]}], "source": ["# Aplicar clipping conforme preprocessing_config.json (sigma/iqr)\n", "outlier_info = {}\n", "for c, meta in cfg.items():\n", "    if c in df_sel.columns and isinstance(meta.get('threshold',{}), dict):\n", "        th = meta['threshold']; lo, hi = th.get('low'), th.get('high')\n", "        before_n = df_sel[c].notna().sum()\n", "        df_sel[c+'_clipped'] = df_sel[c].clip(lower=lo, upper=hi)\n", "        outlier_info[c] = {'method': meta.get('method'), 'low': lo, 'high': hi, 'n': int(before_n)}\n", "print('Outliers tratados:', len(outlier_info))\n"]}, {"cell_type": "markdown", "id": "047e4135", "metadata": {}, "source": ["## 5. Tratamento de Valores Faltantes"]}, {"cell_type": "code", "execution_count": 7, "id": "488593b7", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:48.607970Z", "iopub.status.busy": "2025-09-11T21:09:48.606377Z", "iopub.status.idle": "2025-09-11T21:09:53.280769Z", "shell.execute_reply": "2025-09-11T21:09:53.279784Z"}}, "outputs": [{"data": {"text/plain": ["Dim_Produtos.Segmentacao      0.292902\n", "Dim_Produtos.Shape            0.288747\n", "Dim_Cliente.Bairro_Cliente    0.087966\n", "cidade                        0.064381\n", "cidade_freq                   0.064381\n", "dtype: float64"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.impute import KNNImputer\n", "missing = df_sel.isna().mean().sort_values(ascending=False)\n", "low_missing_cols = [c for c in df_sel.columns if df_sel[c].isna().mean()<=0.05]\n", "for c in low_missing_cols:\n", "    if df_sel[c].dtype.kind in 'ifc': df_sel[c] = df_sel[c].fillna(df_sel[c].median())\n", "    else: df_sel[c] = df_sel[c].fillna(df_sel[c].mode().iloc[0])\n", "# KNN em numéricas restantes com missing\n", "num_for_impute = [c for c in df_sel.select_dtypes(include=['number']).columns if df_sel[c].isna().any()]\n", "if num_for_impute:\n", "    imputer = KNNImputer(n_neighbors=5)\n", "    df_sel[num_for_impute] = imputer.fit_transform(df_sel[num_for_impute])\n", "# Flags de missing relevantes\n", "for c in df.columns:\n", "    if df[c].isna().any(): df_sel[c+'_was_missing'] = df[c].isna().astype(int)\n", "missing.head()\n"]}, {"cell_type": "markdown", "id": "f6eba0c8", "metadata": {}, "source": ["## 6. Codificação de Variáveis Categóricas"]}, {"cell_type": "code", "execution_count": 8, "id": "11cd4a49", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:53.285423Z", "iopub.status.busy": "2025-09-11T21:09:53.284895Z", "iopub.status.idle": "2025-09-11T21:09:53.650346Z", "shell.execute_reply": "2025-09-11T21:09:53.649359Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df_fe shape: (35616, 173)\n"]}], "source": ["# One-hot para baixa cardinalidade; Target encoding opcional (SUPERVISED)\n", "SUPERVISED = True; TARGET_COL = 'valor'  # Ajuste para cenário supervisionado\n", "from sklearn.preprocessing import OneHotEncoder\n", "cat_cols_use = [c for c in cat_cols if df_sel[c].nunique()<=20] if cat_cols else []\n", "try:\n", "    ohe = OneHotEncoder(handle_unknown='ignore', sparse_output=False) if cat_cols_use else None\n", "except TypeError:\n", "    ohe = OneHotEncoder(handle_unknown='ignore', sparse=False) if cat_cols_use else None\n", "X_cat = ohe.fit_transform(df_sel[cat_cols_use]) if cat_cols_use else np.empty((len(df_sel),0))\n", "ohe_cols = (ohe.get_feature_names_out(cat_cols_use).tolist() if cat_cols_use else [])\n", "df_ohe = pd.DataFrame(X_cat, columns=ohe_cols, index=df_sel.index) if cat_cols_use else pd.DataFrame(index=df_sel.index)\n", "# Excluir IDs (e colunas *_clipped originais de ID) da matriz numérica\n", "id_like_all = set(id_like) | {c for c in df_sel.columns if str(c).lower().startswith('id_') or str(c).lower().endswith('_id') or str(c).lower().replace(' ','') in ['id','id_loja','id_produto','id_cliente','id_vendedor','id_faturamento','documento','transacao']}\n", "df_num = df_sel.select_dtypes(include=['number']).drop(columns=[c for c in df_sel.columns if c in id_like_all or c.endswith('_clipped')], errors='ignore').copy()\n", "df_fe = pd.concat([df_num, df_ohe], axis=1)\n", "print('df_fe shape:', df_fe.shape)\n"]}, {"cell_type": "markdown", "id": "55dd04cb", "metadata": {}, "source": ["## 7. Normalização/Padronização"]}, {"cell_type": "code", "execution_count": 9, "id": "4d496956", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:53.653288Z", "iopub.status.busy": "2025-09-11T21:09:53.653288Z", "iopub.status.idle": "2025-09-11T21:09:54.044814Z", "shell.execute_reply": "2025-09-11T21:09:54.043828Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["df_scaled: (35616, 173)\n"]}], "source": ["# Escolha guiada por normalidade (<PERSON> p >0.05 => Z-score; <PERSON><PERSON> con<PERSON><PERSON><PERSON>, <PERSON><PERSON>)\n", "from sklearn.preprocessing import StandardScaler, RobustScaler, MinMaxScaler\n", "pmap = dict(zip(norm_tbl['col'], norm_tbl.get('shapiro_p', []))) if not norm_tbl.empty else {}\n", "scaler_choice = {}\n", "for c in df_fe.columns:\n", "    if c in pmap and isinstance(pmap[c], (int,float)) and pmap[c]>0.05: scaler_choice[c]='z'\n", "    elif df_fe[c].nunique()>2 and df_fe[c].dtype.kind in 'ifc': scaler_choice[c]='robust'\n", "    else: scaler_choice[c]='minmax'\n", "Z, R, M = StandardScaler(), RobustScaler(), MinMaxScaler()\n", "scaled = pd.DataFrame(index=df_fe.index)\n", "for c,m in scaler_choice.items():\n", "    v = df_fe[[c]].values\n", "    if m=='z': scaled[c]=Z.fit_transform(v)\n", "    elif m=='robust': scaled[c]=R.fit_transform(v)\n", "    else: scaled[c]=M.fit_transform(v)\n", "df_scaled = scaled\n", "print('df_scaled:', df_scaled.shape)\n"]}, {"cell_type": "markdown", "id": "6b7cf58a", "metadata": {}, "source": ["## 8. Feature Engineering Avançado"]}, {"cell_type": "code", "execution_count": 10, "id": "f801e459", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:54.047927Z", "iopub.status.busy": "2025-09-11T21:09:54.047927Z", "iopub.status.idle": "2025-09-11T21:09:54.477424Z", "shell.execute_reply": "2025-09-11T21:09:54.476437Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Novas features: 10\n"]}], "source": ["# Exemplos: razões entre pares correlacionados; interações básicas; atributos temporais\n", "fe_info = []\n", "# 8.1 Razões entre pares com |corr|>0.7 (limitar quantidade)\n", "num_cols_sc = [c for c in df_scaled.columns if df_scaled[c].dtype.kind in 'ifc']\n", "if len(num_cols_sc)>1:\n", "    cm = pd.DataFrame(np.corrcoef(df_scaled[num_cols_sc].T), index=num_cols_sc, columns=num_cols_sc).abs()\n", "    pairs = []\n", "    for i,a in enumerate(num_cols_sc):\n", "        for j,b in enumerate(num_cols_sc):\n", "            if j>i and cm.loc[a,b]>0.7 and len(pairs)<5: pairs.append((a,b))\n", "    for a,b in pairs:\n", "        nm = f'ratio_{a}_over_{b}'\n", "        df_scaled[nm] = (df_scaled[a]+1e-9)/(df_scaled[b]+1e-9)\n", "        fe_info.append({'feature': nm, 'type':'ratio', 'just':'|corr|>0.7 entre escaladas'})\n", "# 8.2 Interações polinomiais simples (quadrático de 3 primeiras)\n", "for c in num_cols_sc[:3]:\n", "    nm = f'sq_{c}'; df_scaled[nm] = df_scaled[c]**2; fe_info.append({'feature': nm, 'type':'poly2', 'just':'curvaturas potenciais'})\n", "# 8.3 Atributos temporais\n", "if 'data' in df.columns:\n", "    dt = pd.to_datetime(df['data'])\n", "    df_scaled['dow'] = dt.dt.dayofweek; df_scaled['month'] = dt.dt.month\n", "    fe_info.append({'feature':'dow','type':'temporal','just':'dia da semana'})\n", "    fe_info.append({'feature':'month','type':'temporal','just':'mês'})\n", "print('Novas features:', len(fe_info))\n"]}, {"cell_type": "markdown", "id": "71766890", "metadata": {}, "source": ["## 9. Validação das Transformações"]}, {"cell_type": "code", "execution_count": 11, "id": "1baaadec", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:54.481421Z", "iopub.status.busy": "2025-09-11T21:09:54.480844Z", "iopub.status.idle": "2025-09-11T21:09:58.165801Z", "shell.execute_reply": "2025-09-11T21:09:58.165282Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Conferir distribuições após transformações e correlação final\n", "plt.figure(figsize=(8,6));\n", "sns.heatmap(df_scaled.corr().clip(-1,1), cmap='vlag', center=0); plt.title('Matriz de correlação final');\n", "plt.tight_layout(); plt.savefig(PLOTS_FE_DIR / 'final_corr_heatmap.png', bbox_inches='tight'); plt.show()\n"]}, {"cell_type": "markdown", "id": "ac1b1e04", "metadata": {}, "source": ["## 10. <PERSON> de Dados (se supervisionado)"]}, {"cell_type": "code", "execution_count": 12, "id": "51c337c9", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:09:58.168377Z", "iopub.status.busy": "2025-09-11T21:09:58.168377Z", "iopub.status.idle": "2025-09-11T21:10:02.494674Z", "shell.execute_reply": "2025-09-11T21:10:02.494674Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Splits salvos em data/processed/*.csv\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "if SUPERVISED and TARGET_COL and TARGET_COL in df.columns:\n", "    y = df[TARGET_COL]\n", "    X = df_scaled.copy()\n", "    strat = y if y.nunique()<50 else None\n", "    X_train, X_tmp, y_train, y_tmp = train_test_split(X, y, test_size=0.4, random_state=42, stratify=strat)\n", "    strat2 = y_tmp if (strat is not None) else None\n", "    X_val, X_test, y_val, y_test = train_test_split(X_tmp, y_tmp, test_size=0.5, random_state=42, stratify=strat2)\n", "    X_train.assign(**{TARGET_COL: y_train}).to_csv(PROC_DIR / 'train_set.csv', index=False)\n", "    X_val.assign(**{TARGET_COL: y_val}).to_csv(PROC_DIR / 'validation_set.csv', index=False)\n", "    X_test.assign(**{TARGET_COL: y_test}).to_csv(PROC_DIR / 'test_set.csv', index=False)\n", "    print('Splits salvos em data/processed/*.csv')\n", "    # Documentar distribuições do alvo por split\n", "    import numpy as np, pandas as pd\n", "    def stats(s):\n", "        s = pd.to_numeric(s, errors='coerce').dropna()\n", "        q = s.quantile([0.25,0.5,0.75]) if len(s)>0 else pd.Series([np.nan,np.nan,np.nan], index=[0.25,0.5,0.75])\n", "        return {\n", "            'n': int(s.shape[0]), 'mean': float(s.mean()) if len(s)>0 else np.nan, 'std': float(s.std()) if len(s)>1 else np.nan,\n", "            'min': float(s.min()) if len(s)>0 else np.nan, 'p25': float(q.loc[0.25]), 'p50': float(q.loc[0.5]), 'p75': float(q.loc[0.75]),\n", "            'max': float(s.max()) if len(s)>0 else np.nan\n", "        }\n", "    dist = pd.DataFrame([\n", "        dict(split='train', **stats(y_train)),\n", "        dict(split='validation', **stats(y_val)),\n", "        dict(split='test', **stats(y_test)),\n", "    ])\n", "    dist.to_csv(TABLES_DIR / 'target_distribution_by_split.csv', index=False)\n", "else:\n", "    print('SUPERVISED=False ou TARGET_COL ausente; pulando split.')\n"]}, {"cell_type": "markdown", "id": "d7aa015f", "metadata": {}, "source": ["## 11. Dataset Final e Documentação"]}, {"cell_type": "code", "execution_count": 13, "id": "2a9191f1", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:10:02.497658Z", "iopub.status.busy": "2025-09-11T21:10:02.497658Z", "iopub.status.idle": "2025-09-11T21:10:07.332957Z", "shell.execute_reply": "2025-09-11T21:10:07.332422Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Salvo: ..\\data\\processed\\features_engineered.csv\n", "Resumo salvo em tables/feature_engineering_summary.csv\n"]}], "source": ["# Salvar dataset final e sumário de transformações\n", "final_csv = PROC_DIR / 'features_engineered.csv'\n", "df_scaled.to_csv(final_csv, index=False)\n", "print('Salvo:', final_csv)\n", "# Tabela de features com tipo e transformações aplicadas\n", "summary_rows = []\n", "for c in df_scaled.columns:\n", "    tp = str(df_scaled[c].dtype)\n", "    just = []\n", "    if c in cfg: just.append(cfg[c].get('justification',''))\n", "    if c in transforms: just.append('dist:'+str(transforms[c]))\n", "    summary_rows.append({'feature': c, 'dtype': tp, 'justification': '; '.join([j for j in just if j])})\n", "fe_summary = pd.DataFrame(summary_rows)\n", "fe_summary.to_csv(TABLES_DIR / 'feature_engineering_summary.csv', index=False)\n", "print('Resumo salvo em tables/feature_engineering_summary.csv')\n"]}, {"cell_type": "markdown", "id": "a2676209", "metadata": {}, "source": ["## 12. Exportação para Modelagem"]}, {"cell_type": "code", "execution_count": 14, "id": "02166ff0", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:10:07.334518Z", "iopub.status.busy": "2025-09-11T21:10:07.334518Z", "iopub.status.idle": "2025-09-11T21:10:07.344023Z", "shell.execute_reply": "2025-09-11T21:10:07.344023Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pipeline JSON salvo em tables/transformation_pipeline.json\n"]}], "source": ["# Pipeline reproduzível (JSON simples com escolhas aplicadas)\n", "pipe = {\n", "  'outliers_config': cfg,\n", "  'distribution_transforms': transforms,\n", "  'scaler_choice': {c: 'z/robust/minmax'[0:0] for c in []}  # placeholder; escolhas já aplicadas em df_scaled\n", "}\n", "with open(TABLES_DIR / 'transformation_pipeline.json', 'w', encoding='utf-8') as f:\n", "    json.dump(pipe, f, ensure_ascii=False, indent=2)\n", "print('Pipeline JSON salvo em tables/transformation_pipeline.json')\n"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}