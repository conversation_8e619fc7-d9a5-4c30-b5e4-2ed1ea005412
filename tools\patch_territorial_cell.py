from pathlib import Path
import nbformat as nbf

BASE = Path('.')
NB_PATH = BASE / 'notebooks' / '04_territorial_analysis.ipynb'

md = (
    "## Visualizações de Clusterização Territorial (não supervisionado) e Performance Supervisionada por Cluster\n\n"
    "Integra os resultados de segmentação (Notebook 05) com a modelagem supervisionada (Notebook 03) para:\n"
    "1) visualizar como o algoritmo não supervisionado particiona o território; e 2) avaliar o desempenho do melhor modelo por cluster.\n"
)

code = r'''# Two-stage territorial visualization with robust fallbacks
from pathlib import Path
import pandas as pd, numpy as np
import matplotlib.pyplot as plt, seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import OneHotEncoder, StandardScaler
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.svm import SVR
import warnings; warnings.filterwarnings('ignore')

BASE = Path('.')
if not (BASE/'data'/'processed'/'features_engineered_regional.csv').exists(): BASE = Path('..')
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv' if (BASE/'data'/'processed'/'features_engineered_regional.csv').exists() else BASE/'data'/'processed'/'features_engineered.csv'
REPORTS = BASE/'reports'/'2025-08-15'
PLOTS = REPORTS/'plots'
TABLES = REPORTS/'tables'
PLOTS.mkdir(parents=True, exist_ok=True); TABLES.mkdir(parents=True, exist_ok=True)

# Load main data
df = pd.read_csv(DATA, low_memory=False)
print('Loaded data:', DATA, df.shape)

# 1) Load clustering labels from notebook 05 if available
clust_assign_path = TABLES/'cluster_assignments.csv'
cluster_labels = None
if clust_assign_path.exists():
    try:
        cl = pd.read_csv(clust_assign_path)
        if 'index' in cl.columns:
            cl = cl.set_index('index').sort_index()
        if 'cluster' in cl.columns:
            if len(cl) >= len(df):
                cluster_labels = cl['cluster'].iloc[:len(df)].values
            else:
                cluster_labels = cl['cluster'].reindex(range(len(df))).values
        print('Loaded clustering assignments from', clust_assign_path)
    except Exception as e:
        print('WARN: failed to load cluster assignments ->', e)

# Fallback: build proxy clusters from territorial features if needed
if cluster_labels is None:
    try:
        territorial_cols = []
        if 'UF' in df.columns: territorial_cols.append('UF')
        territorial_cols += [c for c in df.columns if 'REGIAO_CHILLI' in c]
        X_terr = df[territorial_cols].copy() if territorial_cols else pd.DataFrame(index=df.index)
        transformers=[]
        if 'UF' in X_terr.columns:
            transformers.append(('uf_ohe', OneHotEncoder(handle_unknown='ignore'), ['UF']))
        fixed = [c for c in X_terr.columns if c!='UF']
        if fixed:
            transformers.append(('pass', 'passthrough', fixed))
        if not transformers:
            num_cols=[c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
            X_terr = df[num_cols[:8]].copy(); transformers=[('sc','passthrough', X_terr.columns.tolist())]
        pre = ColumnTransformer(transformers)
        X_enc = pre.fit_transform(X_terr)
        from sklearn.cluster import KMeans
        k = 5
        km = KMeans(n_clusters=k, random_state=42, n_init=10)
        cluster_labels = km.fit_predict(X_enc)
        print('Built proxy clusters (k=5) from territorial signals')
    except Exception as e:
        print('ERROR: unable to construct proxy clusters ->', e)
        cluster_labels = np.zeros(len(df), dtype=int)

# 1) Territorial clustering visualization
try:
    map_path = PLOTS/'territorial_clustering_map.png'
    if 'UF' in df.columns:
        order = sorted(df['UF'].dropna().unique())
        uf_cat = pd.Categorical(df['UF'], categories=order, ordered=True)
        plt.figure(figsize=(12,6))
        y = np.random.RandomState(42).rand(len(df))
        sns.scatterplot(x=uf_cat, y=y, hue=cluster_labels, palette='tab10', s=6, alpha=0.5, legend=False)
        sizes = pd.Series(cluster_labels).value_counts().sort_index()
        handles = [plt.Line2D([0],[0], marker='o', color='w', label=f'Cluster {i} (n={sizes.get(i,0)})', markerfacecolor=plt.cm.tab10(i%10), markersize=8) for i in range(int(pd.Series(cluster_labels).max())+1)]
        plt.legend(handles=handles, title='Clusters', bbox_to_anchor=(1.02,1), loc='upper left')
        plt.title('Segmentação Territorial por UF (cores=clusters)')
        plt.xlabel('UF'); plt.ylabel('pos. relativa (visual)'); plt.tight_layout();
        plt.savefig(map_path, dpi=180, bbox_inches='tight'); plt.show()
    else:
        region_oh = [c for c in df.columns if 'REGIAO_CHILLI' in c]
        if region_oh:
            vals = pd.DataFrame(df[region_oh].values, columns=region_oh)
            idx = np.argmax(vals.values, axis=1)
            cats = [region_oh[i] for i in idx]
            cat_series = pd.Series(cats, index=df.index, name='territory')
            order = sorted(list(pd.unique(cat_series)))
            y = np.random.RandomState(42).rand(len(df))
            plt.figure(figsize=(12,6))
            sns.scatterplot(x=pd.Categorical(cat_series, categories=order, ordered=True), y=y, hue=cluster_labels, palette='tab10', s=6, alpha=0.5, legend=False)
            sizes = pd.Series(cluster_labels).value_counts().sort_index()
            handles = [plt.Line2D([0],[0], marker='o', color='w', label=f'Cluster {i} (n={sizes.get(i,0)})', markerfacecolor=plt.cm.tab10(i%10), markersize=8) for i in range(int(pd.Series(cluster_labels).max())+1)]
            plt.legend(handles=handles, title='Clusters', bbox_to_anchor=(1.02,1), loc='upper left')
            plt.title('Segmentação Territorial por Região (REGIAO_CHILLI) — clusters')
            plt.xlabel('Região'); plt.ylabel('pos. relativa (visual)'); plt.tight_layout(); plt.savefig(map_path, dpi=180, bbox_inches='tight'); plt.show()
        else:
            sizes = pd.Series(cluster_labels).value_counts().sort_index()
            plt.figure(figsize=(8,4)); sns.barplot(x=sizes.index, y=sizes.values)
            plt.title('Tamanho dos clusters (fallback)'); plt.xlabel('Cluster'); plt.ylabel('n'); plt.tight_layout(); plt.savefig(map_path, dpi=180); plt.show()
except Exception as e:
    print('WARN: failed to build territorial clustering map ->', e)

# 2) Supervised model performance by cluster
rank_path = TABLES/'algorithm_ranking.csv'
best_model_name = None
if rank_path.exists():
    try:
        rank = pd.read_csv(rank_path)
        if 'rmse_mean' in rank.columns:
            best_model_name = rank.sort_values('rmse_mean').iloc[0]['model']
        elif 'r2_mean' in rank.columns:
            best_model_name = rank.sort_values('r2_mean', ascending=False).iloc[0]['model']
        print('Best model from ranking:', best_model_name)
    except Exception as e:
        print('WARN: could not read ranking ->', e)
if best_model_name is None:
    best_model_name = 'RF'

num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
candidates = [c for c in ['valor','sq_valor','y','sales','revenue'] if c in df.columns]
target = candidates[0] if candidates else (num_cols[0] if num_cols else None)
if target in num_cols:
    num_cols = [c for c in num_cols if c!=target]

from typing import List

def make_model(name:str):
    if name in ['RF','GB','RandomForest']:
        base = RandomForestRegressor(n_estimators=300, random_state=42)
    elif name in ['SVM','SVR']:
        base = SVR(kernel='rbf')
    elif name in ['LR','Linear','LinearRegression']:
        base = LinearRegression()
    else:
        base = RandomForestRegressor(n_estimators=300, random_state=42)
    pre = ColumnTransformer([
        ('num', StandardScaler(with_mean=False), num_cols)
    ], remainder='drop')
    return Pipeline([('pre', pre), ('model', base)])

metrics_rows = []
all_preds = []
if target is None or not num_cols:
    print('ERROR: target or numeric features not available; skipping supervised by cluster')
else:
    uniq = sorted(pd.Series(cluster_labels).dropna().unique())
    for c_id in uniq:
        mask = (pd.Series(cluster_labels)==c_id).values
        dfc = df.loc[mask]
        if len(dfc) < 100:
            print(f'Cluster {int(c_id)}: too few samples ({len(dfc)}), skipping metrics')
            continue
        X = dfc[num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)
        Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)
        pipe = make_model(str(best_model_name))
        try:
            pipe.fit(Xtr, ytr)
            yp = pipe.predict(Xte)
            r2 = float(r2_score(yte, yp))
            rmse = float(np.sqrt(mean_squared_error(yte, yp)))
            mae = float(mean_absolute_error(yte, yp))
            metrics_rows.append({'cluster':int(c_id),'n':int(len(dfc)),'r2':r2,'rmse':rmse,'mae':mae})
            all_preds.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))
        except Exception as e:
            print(f'Cluster {int(c_id)}: training failed ->', e)

    if metrics_rows:
        mdf = pd.DataFrame(metrics_rows).sort_values('cluster')
        mdf.to_csv(TABLES/'supervised_by_cluster_metrics.csv', index=False)
        fig, axes = plt.subplots(1,3, figsize=(14,4))
        sns.barplot(data=mdf, x='cluster', y='r2', ax=axes[0]); axes[0].set_title('R² por Cluster');
        sns.barplot(data=mdf, x='cluster', y='rmse', ax=axes[1]); axes[1].set_title('RMSE por Cluster');
        sns.barplot(data=mdf, x='cluster', y='mae', ax=axes[2]); axes[2].set_title('MAE por Cluster');
        plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_performance.png', dpi=180); plt.show()

    if all_preds:
        preds = pd.concat(all_preds, ignore_index=True)
        preds.to_csv(TABLES/'supervised_by_cluster_predictions.csv', index=False)
        plt.figure(figsize=(6,6));
        sns.scatterplot(data=preds, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)
        lim = (min(preds['y_true'].min(), preds['y_pred'].min()), max(preds['y_true'].max(), preds['y_pred'].max()))
        plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_predictions.png', dpi=180); plt.show()

# Feature importances per cluster (if supported)
try:
    if str(best_model_name) in ['RF','GB','RandomForest'] and num_cols:
        for c_id in sorted(pd.Series(cluster_labels).dropna().unique()):
            mask = (pd.Series(cluster_labels)==c_id).values
            dfc = df.loc[mask]
            if len(dfc) < 200: continue
            X = dfc[num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)
            pipe = make_model('RF'); pipe.fit(X, y)
            model = pipe.named_steps['model']
            if hasattr(model, 'feature_importances_'):
                imps = model.feature_importances_
                top_idx = np.argsort(imps)[-10:][::-1]
                out = pd.DataFrame({'feature':[num_cols[i] for i in top_idx], 'importance':[float(imps[i]) for i in top_idx]})
                out['cluster']=int(c_id)
                out.to_csv(TABLES/f'feature_importances_top10_cluster_{int(c_id)}.csv', index=False)
except Exception as e:
    print('Feature importance by cluster skipped ->', e)
'''

def main():
    if not NB_PATH.exists():
        print('Notebook not found:', NB_PATH)
        return
    nb = nbf.read(NB_PATH, as_version=4)
    nb.cells.append(nbf.v4.new_markdown_cell(md))
    # new_code_cell requires outputs and execution_count optional; nbformat will handle defaults
    nb.cells.append(nbf.v4.new_code_cell(code))
    nbf.write(nb, NB_PATH)
    print('Patched notebook with territorial visualizations')

if __name__ == '__main__':
    main()

