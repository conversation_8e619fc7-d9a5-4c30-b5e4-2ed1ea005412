from pathlib import Path
import matplotlib.pyplot as plt
from PIL import Image

# Add simple footer annotation: fonte, metodologia e callout
# This script overlays a footer box onto existing PNGs without changing the core plot code

REPORTS = Path('reports/2025-08-15')
PLOTS = REPORTS / 'plots'
OUT = PLOTS / 'presentation'
OUT.mkdir(parents=True, exist_ok=True)

FOOTER = ('Fonte: data/processed/features_engineered_regional.csv | Metodologia: modelos com anti-vazamento, split 80-20, IC 95% quando indicado.\n'
          'Nota: Nomes de cluster refletem padrões geográficos/comportamentais; implicações: segmentar ações por cluster.')


def add_footer(src: Path, dst: Path):
    im = Image.open(src).convert('RGB')
    # Create footer band
    import numpy as np
    import PIL.ImageDraw as ImageDraw
    W, H = im.size
    band_h = max(60, int(0.11*H))
    footer = Image.new('RGB', (W, band_h), color=(245,245,245))
    draw = ImageDraw.Draw(footer)
    # wrap text
    import textwrap
    tw = textwrap.fill(FOOTER, width=120)
    draw.text((10, 10), tw, fill=(50,50,50))
    # stack
    out = Image.new('RGB', (W, H+band_h), color=(255,255,255))
    out.paste(im, (0,0)); out.paste(footer, (0,H))
    out.save(dst)

if __name__ == '__main__':
    # pick key plots to annotate
    candidates = [
        'supervised_by_cluster_performance.png',
        'supervised_by_cluster_predictions.png',
        'territorial_clustering_map_multi.png',
    ]
    for name in candidates:
        src = PLOTS / name
        if src.exists():
            dst = OUT / name.replace('.png','_annotated.png')
            add_footer(src, dst)
            print('Annotated ->', dst)

