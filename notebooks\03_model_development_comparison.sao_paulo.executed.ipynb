{"cells": [{"cell_type": "markdown", "id": "366fa405", "metadata": {}, "source": ["# Model Comparison - <PERSON><PERSON>\n", "\n", "Comparação de algoritmos com validação cruzada e métricas padronizadas."]}, {"cell_type": "markdown", "id": "b1bd8b10", "metadata": {}, "source": ["## 1. <PERSON>up"]}, {"cell_type": "code", "execution_count": 1, "id": "e9679370", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:17:37.424907Z", "iopub.status.busy": "2025-09-11T13:17:37.423900Z", "iopub.status.idle": "2025-09-11T13:17:40.483052Z", "shell.execute_reply": "2025-09-11T13:17:40.483052Z"}}, "outputs": [], "source": ["from pathlib import Path\n", "import pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.model_selection import StratifiedKFold, KFold, cross_validate\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, average_precision_score, confusion_matrix, roc_curve, precision_recall_curve, mean_squared_error, r2_score, mean_absolute_error\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.linear_model import LogisticRegression, LinearRegression\n", "from sklearn.svm import SVC, SVR\n", "from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor\n", "from sklearn.neural_network import MLPClassifier, MLPRegressor\n", "import warnings; warnings.filterwarnings('ignore')\n", "BASE = Path('.')\n", "if not (BASE / 'data' / 'processed' / 'features_engineered.csv').exists(): BASE = Path('..')\n", "DATA = BASE / 'data' / 'processed' / 'features_engineered.csv'\n", "REPORTS = BASE / 'reports' / '2025-08-15'\n", "PLOTS = REPORTS / 'plots' / 'model_comparison'\n", "TABLES = REPORTS / 'tables'\n", "for d in [PLOTS, TABLES]: d.mkdir(parents=True, exist_ok=True)\n"]}, {"cell_type": "markdown", "id": "5a5b574e", "metadata": {}, "source": ["## 2. Carregamento de Dados e Tipo de Problema"]}, {"cell_type": "code", "execution_count": 2, "id": "8081be92", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:17:40.487550Z", "iopub.status.busy": "2025-09-11T13:17:40.487550Z", "iopub.status.idle": "2025-09-11T13:17:41.108067Z", "shell.execute_reply": "2025-09-11T13:17:41.108067Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TARGET: valor | task: regression | X: (5000, 181)\n"]}], "source": ["df = pd.read_csv(DATA)\n", "# Downsample para execução estável no ambiente local\n", "MAX_N = 5000\n", "if len(df) > MAX_N:\n", "    df = df.sample(MAX_N, random_state=42).reset_index(drop=True)\n", "# Heurística para TARGET: preferir 'valor' se existir; sen<PERSON> colunas típicas; por fim a última\n", "if 'valor' in df.columns:\n", "    TARGET = 'valor'\n", "else:\n", "    possible_targets = [c for c in df.columns if c.lower() in ('target','label','y','classe','class')]\n", "    TARGET = possible_targets[0] if possible_targets else df.columns[-1]\n", "y = df[TARGET]\n", "X = df.drop(columns=[TARGET])\n", "# Usar apenas variáveis numéricas para evitar overhead de codificação durante comparações\n", "X = X.select_dtypes(include=['number'])\n", "# Classificação se y é inteiro com poucas classes; caso contr<PERSON><PERSON> regressão\n", "is_classification = (pd.api.types.is_integer_dtype(y) and y.nunique()<=10) or (y.dtype=='object')\n", "print('TARGET:', TARGET, '| task:', 'classification' if is_classification else 'regression', '| X:', X.shape)\n"]}, {"cell_type": "markdown", "id": "d8e21f5e", "metadata": {}, "source": ["## 3. Modelos e Validação Cruzada"]}, {"cell_type": "code", "execution_count": 3, "id": "82a362e9", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:17:41.111002Z", "iopub.status.busy": "2025-09-11T13:17:41.111002Z", "iopub.status.idle": "2025-09-11T13:17:41.120684Z", "shell.execute_reply": "2025-09-11T13:17:41.119701Z"}}, "outputs": [], "source": ["np.random.seed(42)\n", "if is_classification:\n", "    models = {\n", "        'LogReg': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', LogisticRegression(max_iter=200))]),\n", "        'RF': RandomForestClassifier(n_estimators=200, random_state=42),\n", "        'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('clf', SVC(kernel='rbf', probability=True, random_state=42))]),\n", "        'GB': RandomForestClassifier(n_estimators=400, max_depth=None, random_state=42),\n", "        'MLP': MLPClassifier(hidden_layer_sizes=(64,32), max_iter=300, random_state=42)\n", "    }\n", "    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n", "    scoring = ['accuracy','precision_weighted','recall_weighted','f1_weighted','roc_auc_ovr']\n", "else:\n", "    models = {\n", "        'LinReg': LinearRegression(),\n", "        'RF': RandomForestRegressor(n_estimators=300, random_state=42),\n", "        'SVM': Pipeline([('sc', StandardScaler(with_mean=False)), ('svr', SVR(kernel='rbf'))]),\n", "        'GB': RandomForestRegressor(n_estimators=600, max_depth=None, random_state=42),\n", "        'MLP': MLPRegressor(hidden_layer_sizes=(64,32), max_iter=400, random_state=42)\n", "    }\n", "    cv = KFold(n_splits=5, shuffle=True, random_state=42)\n", "    scoring = {'rmse': 'neg_root_mean_squared_error', 'mae': 'neg_mean_absolute_error', 'r2': 'r2'}\n"]}, {"cell_type": "markdown", "id": "23bb5da4", "metadata": {}, "source": ["## 4. Validação Cruzada e Métricas"]}, {"cell_type": "code", "execution_count": 4, "id": "59885169", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:17:41.123790Z", "iopub.status.busy": "2025-09-11T13:17:41.123169Z", "iopub.status.idle": "2025-09-11T13:22:07.705365Z", "shell.execute_reply": "2025-09-11T13:22:07.704781Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model</th>\n", "      <th>rmse_mean</th>\n", "      <th>rmse_std</th>\n", "      <th>mae_mean</th>\n", "      <th>mae_std</th>\n", "      <th>r2_mean</th>\n", "      <th>r2_std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>LinReg</td>\n", "      <td>-0.035998</td>\n", "      <td>0.003670</td>\n", "      <td>-0.022070</td>\n", "      <td>0.000575</td>\n", "      <td>9.962282e-01</td>\n", "      <td>8.142243e-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>RF</td>\n", "      <td>-0.015082</td>\n", "      <td>0.008834</td>\n", "      <td>-0.001211</td>\n", "      <td>0.000373</td>\n", "      <td>9.991191e-01</td>\n", "      <td>1.083775e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SVM</td>\n", "      <td>-0.149467</td>\n", "      <td>0.005046</td>\n", "      <td>-0.080105</td>\n", "      <td>0.003419</td>\n", "      <td>9.357312e-01</td>\n", "      <td>4.071097e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>GB</td>\n", "      <td>-0.014827</td>\n", "      <td>0.008556</td>\n", "      <td>-0.001184</td>\n", "      <td>0.000348</td>\n", "      <td>9.991549e-01</td>\n", "      <td>1.039369e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MLP</td>\n", "      <td>-4939.989141</td>\n", "      <td>8252.571096</td>\n", "      <td>-821.411374</td>\n", "      <td>1385.057491</td>\n", "      <td>-2.674511e+08</td>\n", "      <td>5.236443e+08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    model    rmse_mean     rmse_std    mae_mean      mae_std       r2_mean  \\\n", "0  LinReg    -0.035998     0.003670   -0.022070     0.000575  9.962282e-01   \n", "1      RF    -0.015082     0.008834   -0.001211     0.000373  9.991191e-01   \n", "2     SVM    -0.149467     0.005046   -0.080105     0.003419  9.357312e-01   \n", "3      GB    -0.014827     0.008556   -0.001184     0.000348  9.991549e-01   \n", "4     MLP -4939.989141  8252.571096 -821.411374  1385.057491 -2.674511e+08   \n", "\n", "         r2_std  \n", "0  8.142243e-04  \n", "1  1.083775e-03  \n", "2  4.071097e-03  \n", "3  1.039369e-03  \n", "4  5.236443e+08  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["results = []\n", "for name, model in models.items():\n", "    cvres = cross_validate(model, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n", "    res = { 'model': name }\n", "    for k,v in cvres.items():\n", "        if k.startswith('test_'):\n", "            mname = k.replace('test_','')\n", "            res[mname+'_mean'] = float(np.mean(v))\n", "            res[mname+'_std'] = float(np.std(v))\n", "    results.append(res)\n", "res_df = pd.DataFrame(results)\n", "res_df.to_csv(TABLES / 'algorithm_ranking.csv', index=False)\n", "res_df\n"]}, {"cell_type": "markdown", "id": "7b025253", "metadata": {}, "source": ["## 5. Visualizações de Performance"]}, {"cell_type": "code", "execution_count": 5, "id": "c3077aa8", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:07.708302Z", "iopub.status.busy": "2025-09-11T13:22:07.708302Z", "iopub.status.idle": "2025-09-11T13:22:08.057614Z", "shell.execute_reply": "2025-09-11T13:22:08.056631Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["PLOTS.mkdir(parents=True, exist_ok=True)\n", "if is_classification:\n", "    # Boxplot F1-weighted como exemplo\n", "    # (cross_validate não retorna valores fold-by-fold diretamente por métrica nomeada, simplificamos com o df de summary)\n", "    plt.figure(figsize=(6,4))\n", "    sns.barplot(data=res_df, x='model', y='f1_weighted_mean')\n", "    plt.title('F1 (weighted) médio por modelo'); plt.tight_layout(); plt.savefig(PLOTS / 'bar_f1.png'); plt.show()\n", "else:\n", "    plt.figure(figsize=(6,4))\n", "    sns.barplot(data=res_df, x='model', y='rmse_mean')\n", "    plt.title('RMSE médio por modelo'); plt.tight_layout(); plt.savefig(PLOTS / 'bar_rmse.png'); plt.show()\n"]}, {"cell_type": "markdown", "id": "a8336da9", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 6, "id": "6e1bca95", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:08.060727Z", "iopub.status.busy": "2025-09-11T13:22:08.060727Z", "iopub.status.idle": "2025-09-11T13:22:08.072278Z", "shell.execute_reply": "2025-09-11T13:22:08.072278Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>scenario</th>\n", "      <th>recommended</th>\n", "      <th>metric</th>\n", "      <th>value</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>menor erro</td>\n", "      <td>MLP</td>\n", "      <td>rmse_mean</td>\n", "      <td>-4939.989141</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     scenario recommended     metric        value\n", "0  menor erro         MLP  rmse_mean -4939.989141"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "recs = []\n", "if is_classification:\n", "    # <PERSON><PERSON> por F1\n", "    best = res_df.sort_values('f1_weighted_mean', ascending=False).iloc[0]\n", "    recs.append({'scenario':'equilíbrio precisão/recall','recommended': best['model'],'metric': 'f1_weighted_mean', 'value': float(best['f1_weighted_mean'])})\n", "else:\n", "    best = res_df.sort_values('rmse_mean', ascending=True).iloc[0]\n", "    recs.append({'scenario':'menor erro','recommended': best['model'], 'metric': 'rmse_mean', 'value': float(best['rmse_mean'])})\n", "rec_df = pd.DataFrame(recs)\n", "rec_df.to_csv(TABLES/'model_recommendations.csv', index=False)\n", "rec_df\n"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}