import argparse
import json
import time
from pathlib import Path
import nbformat
from nbconvert.preprocessors import ExecutePreprocessor
import pandas as pd

REPORTS_DIR = Path('reports') / '2025-08-15'
TABLES_DIR = REPORTS_DIR / 'tables'
PLOTS_DIR = REPORTS_DIR / 'plots'
TABLES_DIR.mkdir(parents=True, exist_ok=True)

NOTEBOOKS = {
    'eda': Path('notebooks/chilli_beans_analysis.ipynb'),
    'feature_engineering': Path('notebooks/feature_engineering.ipynb'),
}

EXPECTED = {
    'eda': {
        'tables': [
            'normality_tests.csv',
            'outlier_filtering_impact.csv',
            'outlier_method_recommendations.csv',
            'bootstrap_confidence_intervals.csv',
        ],
        'plots': [
            ('eda/heatmap_corr_spearman.png'),
            ('eda/heatmap_corr_pearson.png'),
            ('eda/outliers/box_theoretical_*'),
        ],
    },
    'feature_engineering': {
        'tables': [
            'feature_engineering_summary.csv',
            'transformation_pipeline.json',
        ],
        'plots': [
            'feature_engineering/final_corr_heatmap.png',
        ],
        'processed': [
            Path('data/processed/features_engineered.csv'),
        ],
    },
}


def execute_notebook(nb_path: Path, timeout=900) -> nbformat.NotebookNode:
    with open(nb_path, encoding='utf-8') as f:
        nb = nbformat.read(f, as_version=4)
    ep = ExecutePreprocessor(timeout=timeout, kernel_name='python3')
    ep.preprocess(nb, {'metadata': {'path': str(nb_path.parent)}})
    return nb


def scan_errors(nb: nbformat.NotebookNode):
    errors = []
    for i, cell in enumerate(nb.cells):
        if cell.cell_type != 'code':
            continue
        outputs = cell.get('outputs', [])
        for out in outputs:
            if out.output_type == 'error':
                errors.append({
                    'cell_index': i,
                    'ename': out.get('ename'),
                    'evalue': out.get('evalue'),
                    'traceback': '\n'.join(out.get('traceback', []))[:2000],
                })
    return errors


def check_artifacts(kind: str):
    """Return list of dict results for artifact checks."""
    res = []
    exp = EXPECTED.get(kind, {})
    # tables
    for name in exp.get('tables', []):
        p = TABLES_DIR / name
        res.append({'check': f'table:{name}', 'exists': p.exists(), 'path': str(p)})
    # plots (allow simple wildcard suffix)
    for name in exp.get('plots', []):
        p = PLOTS_DIR / name
        if '*' in name:
            base = str(p)
            matches = list(p.parent.glob(p.name))
            ok = len(matches) > 0
            res.append({'check': f'plot:{name}', 'exists': ok, 'path': base, 'matches': len(matches)})
        else:
            res.append({'check': f'plot:{name}', 'exists': p.exists(), 'path': str(p)})
    # processed
    for p in exp.get('processed', []):
        res.append({'check': f'processed:{p.name}', 'exists': Path(p).exists(), 'path': str(p)})
    return res


def validate_dataframes(kind: str):
    """Perform lightweight validations by reading final outputs."""
    results = []
    # For feature_engineering, read features_engineered.csv and check shape
    if kind == 'feature_engineering':
        p = Path('data/processed/features_engineered.csv')
        if p.exists():
            try:
                df = pd.read_csv(p)
                results.append({'check': 'df_shape>0', 'ok': (df.shape[0] > 0 and df.shape[1] > 0), 'shape': str(df.shape)})
                # Type sanity: at least one numeric column
                has_num = any(pd.api.types.is_numeric_dtype(df[c]) for c in df.columns)
                results.append({'check': 'has_numeric', 'ok': has_num})
            except Exception as e:
                results.append({'check': 'read_features_engineered', 'ok': False, 'error': str(e)})
        else:
            results.append({'check': 'features_engineered_exists', 'ok': False})
    return results


def run_one(kind: str):
    nb_path = NOTEBOOKS[kind]
    started = time.time()
    status = 'OK'
    err_list = []
    try:
        nb_exec = execute_notebook(nb_path)
        err_list = scan_errors(nb_exec)
        if err_list:
            status = 'ERRORS_IN_CELLS'
    except Exception as e:
        status = 'EXECUTION_FAILED'
        err_list = [{'error': str(e)}]
    artifacts = check_artifacts(kind)
    df_checks = validate_dataframes(kind)
    elapsed = time.time() - started
    return {
        'notebook': str(nb_path),
        'status': status,
        'elapsed_sec': round(elapsed, 2),
        'cell_errors': err_list,
        'artifact_checks': artifacts,
        'df_checks': df_checks,
    }


def main():
    parser = argparse.ArgumentParser(description='Test notebooks execution and artifacts')
    g = parser.add_mutually_exclusive_group(required=True)
    g.add_argument('--all', action='store_true', help='Run all notebooks tests')
    g.add_argument('--notebook', choices=list(NOTEBOOKS.keys()), help='Run a specific notebook')
    args = parser.parse_args()

    kinds = list(NOTEBOOKS.keys()) if args.all else [args.notebook]
    results_rows = []
    full_report = []
    for kind in kinds:
        r = run_one(kind)
        full_report.append(r)
        # Flatten artifact checks for CSV
        if r['cell_errors']:
            for ce in r['cell_errors']:
                results_rows.append({
                    'notebook': kind,
                    'item': f"cell_error_{ce.get('cell_index','na')}",
                    'result': 'FAIL',
                    'detail': ce.get('evalue') or ce.get('error'),
                })
        for ac in r['artifact_checks']:
            results_rows.append({
                'notebook': kind,
                'item': ac['check'],
                'result': 'OK' if ac['exists'] else 'MISSING',
                'detail': ac.get('path'),
            })
        for dc in r['df_checks']:
            results_rows.append({
                'notebook': kind,
                'item': dc['check'],
                'result': 'OK' if dc.get('ok') else 'FAIL',
                'detail': dc.get('shape') or dc.get('error') or '',
            })

    # Save CSV report
    out_csv = TABLES_DIR / 'notebook_test_results.csv'
    pd.DataFrame(results_rows).to_csv(out_csv, index=False)
    print('Notebook tests report saved at', out_csv)

    # Save JSON with full detail
    out_json = TABLES_DIR / 'notebook_test_results.json'
    out_json.write_text(json.dumps(full_report, ensure_ascii=False, indent=2), encoding='utf-8')
    print('Notebook tests JSON saved at', out_json)


if __name__ == '__main__':
    main()

