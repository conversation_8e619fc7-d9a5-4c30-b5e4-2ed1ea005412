import nbformat as nbf
from pathlib import Path

BASE = Path('.')
NB_PATH = BASE/'notebooks'/'presentation'/'sprint3_demo.ipynb'
NB_PATH.parent.mkdir(parents=True, exist_ok=True)

nb = nbf.v4.new_notebook()

cells = []

cells.append(nbf.v4.new_markdown_cell(
"""
# Sprint 3 — Data Preparation & Modeling (Hybrid)

Objetivo: demonstrar o processo técnico (código) com foco em:
- Engenharia de atributos com anti-leakage (split-aware)
- Seleção de K (Elbow, Silhouette, Gap Statistic)
- Pipeline híbrido (clustering + modelagem supervisionada)
- Métricas com IC95% e interpretação de negócio
"""))

cells.append(nbf.v4.new_code_cell(
"""
from pathlib import Path
import pandas as pd, numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import matplotlib.pyplot as plt
import seaborn as sns

BASE = Path('.')
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'
PLOTS = REPORTS/'plots'
PRESENT = PLOTS/'presentation'
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'

df = pd.read_csv(DATA, low_memory=False)
print('Shape:', df.shape)
"""))

cells.append(nbf.v4.new_markdown_cell(
"""
## 1) Engenharia de atributos com anti-leakage (split-aware)

- Seleção de colunas feita apenas no conjunto de treino (evita olhar o teste)
- Remoção de colunas com correlação alta com o alvo e padrões suspeitos (ex.: `valor`, `store_avg_valor`, `UF_`, `sp_`, etc.)
- Reuso do módulo tools/feature_filter.py
"""))

cells.append(nbf.v4.new_code_cell(
"""
from tools.feature_filter import compute_clean_features

TARGET = 'valor'
# Split base (estratificação simples por quantis do alvo, se desejável)
idx = np.arange(len(df))
idx_tr, idx_te = train_test_split(idx, test_size=0.2, random_state=42)
df_tr = df.iloc[idx_tr].copy(); df_te = df.iloc[idx_te].copy()

clean_cols, audit_removed = compute_clean_features(
    df_tr, TARGET, REPORTS,
    corr_threshold=0.95,
    suspicious_additional=['sp_', 'uf_', 'estado_', 'regiao_', 'loja_', 'franquia_']
)
print('N clean cols (train):', len(clean_cols))
# Aplica mesma seleção no teste
X_tr = df_tr[clean_cols].fillna(0.0)
X_te = df_te[clean_cols].fillna(0.0)
y_tr = df_tr[TARGET].values; y_te = df_te[TARGET].values

scaler = StandardScaler()
X_trs = scaler.fit_transform(X_tr)
X_tes = scaler.transform(X_te)
"""))

cells.append(nbf.v4.new_markdown_cell(
"""
## 2) Seleção de K (Elbow, Silhouette, Gap Statistic)

Avaliação para K=2..10, comparando o método atual (labels existentes) com K-Means.
- Elbow: inércia do K-Means
- Silhouette: qualidade de separação
- Gap Statistic (aproximação bootstrap)
"""))

cells.append(nbf.v4.new_code_cell(
"""
# Labels atuais (se existirem)
assign_path = TABLES/'cluster_assignments.csv'
if assign_path.exists():
    lab = pd.read_csv(assign_path)
    labels_current = lab['cluster'].values if 'cluster' in lab.columns else None
else:
    labels_current = None

Xk = X_trs  # usa features de treino padronizadas para validação
Ks = list(range(2,11))
inertias = []
sil_km = []
for k in Ks:
    km = KMeans(n_clusters=k, n_init='auto', random_state=42)
    km_labels = km.fit_predict(Xk)
    inertias.append(float(km.inertia_))
    sil_km.append(float(silhouette_score(Xk, km_labels)))

sil_current = None
if labels_current is not None and len(set(labels_current))>1:
    # calcula silhouette do método atual nas mesmas features
    sil_current = float(silhouette_score(Xk, labels_current[:len(Xk)]))

fig, axes = plt.subplots(1,2, figsize=(12,4))
axes[0].plot(Ks, inertias, marker='o'); axes[0].set_title('Elbow — Inércia K-Means'); axes[0].set_xlabel('K'); axes[0].set_ylabel('Inércia')
axes[1].plot(Ks, sil_km, marker='o', label='K-Means')
if sil_current is not None: axes[1].axhline(sil_current, color='orange', linestyle='--', label='Atual')
axes[1].set_title('Silhouette — K-Means vs Atual'); axes[1].set_xlabel('K'); axes[1].set_ylabel('Silhouette'); axes[1].legend()
plt.show()
"""))

cells.append(nbf.v4.new_code_cell(
"""
# Gap Statistic (aproximação):
# Para cada K, compara log(Wk) com log(Wk*) de amostras de referência (uniformes no espaço dos dados padronizados)
import numpy as np
rng = np.random.default_rng(42)
B = 10  # número de amostras de referência (pode aumentar em execução offline)

mins = Xk.min(axis=0); maxs = Xk.max(axis=0)

def within_dispersion(X, labels):
    # soma das distâncias quadráticas intra-cluster
    s = 0.0
    for c in np.unique(labels):
        Xi = X[labels==c]
        if len(Xi)==0: continue
        mu = Xi.mean(axis=0)
        s += float(((Xi - mu)**2).sum())
    return s

logWks = []
logWkbs = []
sks = []
for k in Ks:
    km = KMeans(n_clusters=k, n_init='auto', random_state=42).fit(Xk)
    labels = km.labels_
    Wk = within_dispersion(Xk, labels)
    logWks.append(np.log(Wk))
    # referência
    ref_vals = []
    for b in range(B):
        Xref = rng.uniform(mins, maxs, size=Xk.shape)
        kmr = KMeans(n_clusters=k, n_init='auto', random_state=42).fit(Xref)
        Wkb = within_dispersion(Xref, kmr.labels_)
        ref_vals.append(np.log(Wkb))
    ref_vals = np.array(ref_vals)
    logWkbs.append(ref_vals.mean())
    sks.append(ref_vals.std()*np.sqrt(1****/B))

gaps = np.array(logWkbs) - np.array(logWks)

plt.figure(figsize=(6,4))
plt.plot(Ks, gaps, marker='o'); plt.title('Gap Statistic'); plt.xlabel('K'); plt.ylabel('Gap')
plt.show()

# Regra de escolha (Tibshirani): menor K tal que Gap(K) >= Gap(K+1) - s_{K+1}
chosen_K = Ks[np.argmax([gaps[i] - (gaps[i+1]-sks[i+1] if i < len(Ks)-1 else 0) for i in range(len(Ks))])]
print('K escolhido (regra Gap):', chosen_K)
"""))

cells.append(nbf.v4.new_markdown_cell(
"""
## 3) Pipeline Híbrido: Clustering + Supervisionado

- Clustering define segmentos; modelos supervisionados são treinados por segmento
- Anti‑leakage: seleção de features no treino por cluster
- Métricas por cluster com IC 95% e diagnósticos
"""))

cells.append(nbf.v4.new_code_cell(
"""
from tools.feature_filter import compute_clean_features
from sklearn.model_selection import KFold

# Exemplo simples: treinar regressão linear por cluster atual ou por K escolhido
if assign_path.exists():
    base_labels = labels_current[:len(df_tr)] if labels_current is not None else None
else:
    base_labels = None

if base_labels is None:
    # gera labels por K-Means com K escolhido
    km_all = KMeans(n_clusters=int(chosen_K), n_init='auto', random_state=42)
    base_labels = km_all.fit_predict(StandardScaler().fit_transform(df[clean_cols].fillna(0.0)))

clusters = sorted(np.unique(base_labels))
res = []
for c in clusters:
    idxc = np.where(base_labels==c)[0]
    if len(idxc) < 60:
        continue
    dfc = df.iloc[idxc].copy()
    y = dfc[TARGET].values
    kf = KFold(n_splits=5, shuffle=True, random_state=42)
    r2s=[]; rmses=[]; maes=[]
    for tr, te in kf.split(dfc):
        df_trc = dfc.iloc[tr].copy(); df_tec = dfc.iloc[te].copy()
        clean_c, _ = compute_clean_features(df_trc, TARGET, REPORTS, corr_threshold=0.95,
                                           suspicious_additional=['sp_','uf_','estado_','regiao_','loja_','franquia_'])
        Xtr = df_trc[clean_c].fillna(0.0); Xte = df_tec[clean_c].fillna(0.0)
        sc = StandardScaler(); Xtr = sc.fit_transform(Xtr); Xte = sc.transform(Xte)
        m = LinearRegression().fit(Xtr, df_trc[TARGET])
        yp = m.predict(Xte)
        r2s.append(r2_score(df_tec[TARGET], yp))
        rmses.append(mean_squared_error(df_tec[TARGET], yp, squared=False))
        maes.append(mean_absolute_error(df_tec[TARGET], yp))
    # IC95% (bootstrap normal approx.)
    import numpy as np
    def ci(arr):
        arr = np.array(arr, dtype=float)
        m = np.nanmean(arr); se = (np.nanstd(arr, ddof=1) / np.sqrt(max(1,len(arr))))
        return m, m-1.96*se, m******se
    r2m, r2l, r2h = ci(r2s); rmsem, rmsel, rmseh = ci(rmses); maem, mael, maeh = ci(maes)
    res.append({'cluster': int(c), 'n': len(idxc), 'r2': r2m, 'r2_l': r2l, 'r2_h': r2h,
                'rmse': rmsem, 'rmse_l': rmsel, 'rmse_h': rmseh,
                'mae': maem, 'mae_l': mael, 'mae_h': maeh})

res = pd.DataFrame(res)
res.sort_values('r2', ascending=False).head()
"""))

cells.append(nbf.v4.new_markdown_cell(
"""
## 4) Métricas com IC 95% e interpretação de negócio

- KPIs principais: R² (qualidade explicativa), RMSE/MAE (erros)
- IC 95% indica confiabilidade; Q‑Q plots ajudam a avaliar normalidade de resíduos
- Interpretação: clusters com R² alto e erros baixos → priorização de alocação e campanhas; erros altos → revisar features/dados
"""))

cells.append(nbf.v4.new_code_cell(
"""
import matplotlib.pyplot as plt
import seaborn as sns

if not res.empty:
    fig, axes = plt.subplots(1,3, figsize=(14,4))
    o = res.sort_values('r2', ascending=False)
    axes[0].bar(o['cluster'].astype(str), o['r2'])
    axes[0].set_title('R² por Cluster'); axes[0].set_ylim(0,1)

    axes[1].plot(o['cluster'].astype(str), o['rmse'], marker='o', label='RMSE')
    axes[1].plot(o['cluster'].astype(str), o['mae'], marker='s', label='MAE')
    axes[1].legend(); axes[1].set_title('Erros por Cluster')

    axes[2].axis('off')
    axes[2].text(0,1, 'So What?:\n- Priorizar clusters fortes\n- Investigar clusters com erro alto\n- Usar K selecionado p/ estabilidade', va='top')
    plt.show()
else:
    print('Sem resultados suficientes para plot.')
"""))

nb['cells'] = cells

with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)

print('Notebook gerado em', NB_PATH)

