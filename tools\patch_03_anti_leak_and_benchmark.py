from pathlib import Path
import nbformat as nbf

NB = Path('notebooks') / '03_model_development_comparison.ipynb'

ANTI_LEAK_CELL = r'''# Anti-leakage filtering for supervised training + optional benchmarking wrapper
import re, numpy as np, pandas as pd
from pathlib import Path

# Ensure TARGET and derived features are excluded from X
base_targets = ['valor','revenue','sales','y']
if 'TARGET' in globals() and TARGET not in base_targets:
    base_targets = [TARGET] + base_targets

def _is_leak(c: str) -> bool:
    cl = c.lower()
    pats=[]
    for t in base_targets:
        t=t.lower(); pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in cl for p in pats)

X = X[[c for c in X.columns if not _is_leak(c)]]
print('X after anti-leak filter:', X.shape)

# Optional: standardized benchmarking with confidence intervals
try:
    from tools.benchmarking import benchmark_models
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.svm import SVR
    from sklearn.linear_model import LinearRegression
    from sklearn.neural_network import MLPRegressor
    models = {
      'LinReg': LinearRegression(),
      'RF': RandomForestRegressor(n_estimators=200, random_state=42),
      'SVM': SVR(kernel='rbf'),
      'MLP': MLPRegressor(hidden_layer_sizes=(64,32), max_iter=400, random_state=42)
    }
    param_grids = {
      'RF': {'n_estimators':[150,300]},
      'SVM': {'C':[0.5,1], 'gamma':['scale','auto']},
      'MLP': {'hidden_layer_sizes':[(64,32)], 'alpha':[0.0001,0.001]},
    }
    # sample to speed up if large
    Xb = X.copy(); yb = y.copy()
    if len(Xb) > 8000:
        idx = np.random.RandomState(42).choice(len(Xb), 8000, replace=False)
        Xb = Xb.iloc[idx]; yb = yb.iloc[idx]
    dfbench = benchmark_models(Xb, yb, models, param_grids, cv_splits=3)
    dfbench.to_csv(TABLES/'algorithm_ranking_ci.csv', index=False)
    print('Saved benchmarking with CIs ->', TABLES/'algorithm_ranking_ci.csv')
except Exception as e:
    print('Benchmarking wrapper skipped ->', e)
'''

if __name__ == '__main__':
    nb = nbf.read(NB, as_version=4)
    # Insert after data loading cell (look for 'Carregamento de Dados')
    insert_idx = None
    for i, c in enumerate(nb.cells):
        if c.cell_type=='code' and 'X = X.select_dtypes' in (c.source or ''):
            insert_idx = i+1
            break
    if insert_idx is None:
        nb.cells.append(nbf.v4.new_code_cell(ANTI_LEAK_CELL))
    else:
        nb.cells.insert(insert_idx, nbf.v4.new_code_cell(ANTI_LEAK_CELL))
    nbf.write(nb, NB)
    print('Patched 03 with anti-leak + benchmarking cell')

