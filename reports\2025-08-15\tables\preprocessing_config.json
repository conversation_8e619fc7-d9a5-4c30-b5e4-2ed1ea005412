{"Preco_Custo": {"method": "iqr", "threshold": {"low": -64.78, "high": 197.3}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "valor": {"method": "iqr", "threshold": {"low": -38999.5, "high": 87396.5}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Preco_Varejo": {"method": "iqr", "threshold": {"low": -389.995, "high": 873.965}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Frete": {"method": "iqr", "threshold": {"low": 0.0, "high": 0.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Num_Vale": {"method": "iqr", "threshold": {"low": 0.0, "high": 0.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}, "Desconto": {"method": "iqr", "threshold": {"low": 0.0, "high": 0.0}, "justification": "p=0.000 ≤ 0.05; usar IQR (robusto)"}}