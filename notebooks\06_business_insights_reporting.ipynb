{"cells": [{"cell_type": "markdown", "id": "3e127706", "metadata": {}, "source": ["# 06 - Business Insights & Reporting\\n\\nThis notebook compiles executive summaries, KPIs, and final visualizations.\\n"]}, {"cell_type": "code", "execution_count": null, "id": "c4d3a237", "metadata": {}, "outputs": [], "source": ["# Setup paths\\nfrom pathlib import Path\\nBASE = Path('.')\\nREPORTS = BASE / 'reports' / '2025-08-15'\\n(REPORTS/ 'tables').mkdir(parents=True, exist_ok=True)\\n(REPORTS/ 'plots').mkdir(parents=True, exist_ok=True)\\n(REPORTS/ 'models').mkdir(parents=True, exist_ok=True)\\nprint('Reports dir:', REPORTS)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}