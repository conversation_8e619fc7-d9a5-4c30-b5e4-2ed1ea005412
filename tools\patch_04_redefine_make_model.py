from pathlib import Path
import nbformat as nbf

NB = Path('notebooks') / '04_territorial_analysis.ipynb'

PATCH_CODE = r'''# Override make_model and feature list to safe version (no ColumnTransformer)
import pandas as pd
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.linear_model import LinearRegression

def _is_leak_feat(c: str, targets=('valor','revenue','sales','y')):
    cl = c.lower()
    pats = []
    for t in targets:
        t=t.lower(); pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in cl for p in pats)

# recompute num_cols cleanly from current df
if 'num_cols' in globals() and isinstance(num_cols, list) and num_cols:
    _cands = [c for c in num_cols if c in df.columns]
else:
    _cands = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
if 'target' in globals() and target is not None:
    base_targets = [target, 'valor','revenue','sales','y']
else:
    base_targets = ['valor','revenue','sales','y']
num_cols = [c for c in _cands if (c not in base_targets and not _is_leak_feat(c, base_targets))]


def make_model(name:str):
    if name in ['RF','GB','RandomForest']:
        base = RandomForestRegressor(n_estimators=300, random_state=42)
    elif name in ['SVM','SVR']:
        base = SVR(kernel='rbf')
    elif name in ['LR','Linear','LinearRegression']:
        base = LinearRegression()
    else:
        base = RandomForestRegressor(n_estimators=300, random_state=42)
    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])
print('make_model overridden with safe estimator; features:', len(num_cols))
'''

if __name__ == '__main__':
    nb = nbf.read(NB, as_version=4)
    idx_to_patch = None
    for i, c in enumerate(nb.cells):
        if c.cell_type=='code' and 'def make_model' in (c.source or ''):
            idx_to_patch = i
            break
    if idx_to_patch is None:
        nb.cells.insert(0, nbf.v4.new_code_cell(PATCH_CODE))
    else:
        nb.cells.insert(idx_to_patch, nbf.v4.new_code_cell(PATCH_CODE))
    nbf.write(nb, NB)
    print('Inserted safe make_model override before original definition')

