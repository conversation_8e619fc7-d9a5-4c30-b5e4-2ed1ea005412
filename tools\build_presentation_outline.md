## Estrutura sugerida da apresentação (MVP ChilliAnalyzer)

1. Problema & Contexto
   - Desafio: previsão de valor de venda por cluster territorial e de comportamento
   - Riscos identificados: vazamento de dados levando a R² irrealista
   - Objetivo de negócio: comparar SP vs Resto do Brasil e direcionar ações por segmento

2. Metodologia (em linguagem de negócio)
   - Higienização de dados e EDA (01–02) — principais padrões
   - Segmentação de clusters (05) — agrupamentos por território e comportamento
   - Modelagem supervisionada por cluster (04) — avaliação isolada por segmento
   - Anti-vazamento endurecido: remoção de proxies/IDs e correlação alta

3. Principais Resultados
   - Mapa territorial com clusters e nomes de negócio
   - Desempenho por cluster com IC 95% (R² < 0,9, RMSE e MAE)
   - Curvas de aprendizado e resíduos por cluster
   - Comparativo SP vs Resto do Brasil: diferenças de desempenho

4. Implicações de Negócio (So what?)
   - Quais clusters representam maior potencial de receita? Maior precisão?
   - Recomendações por cluster: sortimento, pricing, CRM, localização
   - Ganhos da mitigação de vazamento: métricas realistas e decisões mais confiáveis

5. Próximos Passos
   - Testar modelos adicionais com o wrapper de benchmarking
   - Enriquecer dados com indicadores externos (renda local, concorrência)
   - Ajustar heurísticas de nomes de cluster com inputs das áreas

Anexos
- Auditoria de features: removidas e motivos; top 20 correlações pós‑limpeza
- Tabelas completas de métricas por cluster e predições
- Metodologia detalhada, amostras e limitações

