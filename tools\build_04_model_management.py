import nbformat as nbf
from pathlib import Path

md = lambda t: nbf.v4.new_markdown_cell(t)
code = lambda t: nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / '04_model_management.ipynb'

nb = nbf.v4.new_notebook()

nb.cells.append(md(
    "# Model Management – ChilliAnalyzer MVP (Juliana)\n\n"
    "Objetivo: consolidar métricas, monitorar performance e facilitar retreinamento dos 3 modelos anteriores."
))

# 1. Setup
nb.cells.append(md('## 1. Setup'))
nb.cells.append(code(
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np\n"
    "import matplotlib.pyplot as plt\n"
    "import seaborn as sns\n"
    "import joblib\n"
    "BASE = Path('.')\n"
    "if not (BASE/'reports'/'2025-08-15').exists(): BASE = Path('..')\n"
    "ROOT = BASE/'reports'/'2025-08-15'\n"
    "print('ROOT:', ROOT)\n"
))

# 2. Load artifacts
nb.cells.append(md('## 2. Carregamento de artefatos e métricas'))
nb.cells.append(code(
    "territ = ROOT/'territorial_analysis'\n"
    "cust = ROOT/'customer_clustering'\n"
    "presc = ROOT/'prescription_leads'\n"
    "dfs = {}\n"
    "if (territ/'validation_metrics.csv').exists(): dfs['territorial'] = pd.read_csv(territ/'validation_metrics.csv')\n"
    "if (cust/'cluster_profiles.csv').exists(): dfs['customer'] = pd.read_csv(cust/'cluster_profiles.csv', nrows=5)\n"
    "if (presc/'cv_metrics.csv').exists(): dfs['prescription'] = pd.read_csv(presc/'cv_metrics.csv')\n"
    "for k,v in dfs.items(): print(k, v.head(2))\n"
))

# 3. Dashboard simples de métricas
nb.cells.append(md('## 3. Dashboard simples de métricas'))
nb.cells.append(code(
    "plt.figure(figsize=(6,4))\n"
    "if 'prescription' in dfs:\n"
    "    m = dfs['prescription'].T\n"
    "    m.columns = ['valor']\n"
    "    m.plot(kind='bar'); plt.title('Métricas – Prescription'); plt.tight_layout(); plt.savefig(ROOT/'model_management'/'metrics_prescription.png', dpi=120)\n"
    "else:\n"
    "    print('Sem métricas de prescription')\n"
))

# 4. Drift checks (simples)
nb.cells.append(md('## 4. Checagens simples de drift'))
nb.cells.append(code(
    "import pandas as pd\n"
    "from pathlib import Path\n"
    "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n"
    "df = pd.read_csv(DATA)\n"
    "num = df.select_dtypes(include=['number']).sample(n=min(5, df.shape[1]-1), axis=1, random_state=42)\n"
    "desc = num.describe()\n"
    "out = ROOT/'model_management'\n"
    "out.mkdir(parents=True, exist_ok=True)\n"
    "desc.to_csv(out/'drift_snapshot.csv')\n"
    "desc.head()\n"
))

# 5. Retreinamento (gatilho)
nb.cells.append(md('## 5. Retreinamento – gatilho'))
nb.cells.append(code(
    "print('Placeholder para rotina programática de retreinamento: carregar dados, reexecutar notebooks e salvar novos modelos em models/.')\n"
))

# 6. Relatório executivo
nb.cells.append(md('## 6. Relatório Executivo'))
nb.cells.append(code(
    "summary = []\n"
    "if 'territorial' in dfs: summary.append({'projeto':'Territorial','indicador':'RMSE/MAE','fonte':'territorial_analysis/validation_metrics.csv'})\n"
    "if 'customer' in dfs: summary.append({'projeto':'Customer','indicador':'clusters/perfis','fonte':'customer_clustering/cluster_profiles.csv'})\n"
    "if 'prescription' in dfs: summary.append({'projeto':'Prescription','indicador':'ROC-AUC/F1/AP','fonte':'prescription_leads/cv_metrics.csv'})\n"
    "pd.DataFrame(summary).to_csv(ROOT/'model_management'/'executive_summary.csv', index=False)\n"
    "print('Relatório salvo em', ROOT/'model_management'/'executive_summary.csv')\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook built at', NB_PATH)

