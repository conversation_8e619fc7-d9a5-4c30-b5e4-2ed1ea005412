import nbformat as nbf
from pathlib import Path

md = lambda t: nbf.v4.new_markdown_cell(t)
code = lambda t: nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / '01_territorial_analysis_comprehensive.ipynb'

nb = nbf.v4.new_notebook()

nb.cells.append(md(
    "# Análise Territorial Abrangente – ChilliAnalyzer (Vanessa)\n\n"
    "**Objetivo:** Comparar abordagens supervisionadas e não-supervisionadas para ranquear regiões por potencial de expansão.\n\n"
    "Este notebook demonstra ambas as metodologias, permitindo à equipe escolher a estratégia mais adequada conforme o contexto de negócio."
))

# 1. Setup e Análise Exploratória Geográfica
nb.cells.append(md('## 1. Análise Exploratória Geográfica'))
nb.cells.append(code(
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np, json\n"
    "import matplotlib.pyplot as plt, seaborn as sns\n"
    "from sklearn.ensemble import RandomForestRegressor\n"
    "from sklearn.cluster import KMeans\n"
    "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n"
    "from sklearn.model_selection import KFold, cross_validate\n"
    "from sklearn.metrics import silhouette_score\n"
    "import warnings; warnings.filterwarnings('ignore')\n"
    "import joblib\n"
    "BASE = Path('.')\n"
    "if not (BASE/'data'/'processed'/'features_engineered.csv').exists(): BASE = Path('..')\n"
    "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n"
    "REPORTS = BASE/'reports'/'2025-08-15'/'territorial_analysis'\n"
    "MODELS = BASE/'models'\n"
    "for d in [REPORTS, MODELS]: d.mkdir(parents=True, exist_ok=True)\n"
    "print('Carregando dados de:', DATA)\n"
))

nb.cells.append(code(
    "# Carregamento e preparação inicial\n"
    "df = pd.read_csv(DATA, low_memory=False)\n"
    "print('Dataset shape:', df.shape)\n"
    "# Identificar colunas regionais disponíveis (buscar por padrões)\n"
    "regional_candidates = ['regiao','uf','cidade','bairro','latitude','longitude','Tipo_PDV']\n"
    "# Buscar por colunas que contenham padrões regionais\n"
    "regional_patterns = ['regiao', 'uf', 'cidade', 'tipo_pdv', 'canal', 'loja']\n"
    "available_regional = []\n"
    "for col in df.columns:\n"
    "    col_lower = col.lower().replace('_', '').replace('.', '')\n"
    "    if any(pattern in col_lower for pattern in regional_patterns):\n"
    "        available_regional.append(col)\n"
    "print('Colunas regionais disponíveis:', available_regional[:10], '...' if len(available_regional)>10 else '')\n"
    "# Definir chave de agregação (priorizar colunas categóricas com boa cardinalidade)\n"
    "GROUP_KEY = None\n"
    "for col in available_regional:\n"
    "    if df[col].dtype == 'object' or (df[col].dtype in ['int64','float64'] and df[col].nunique() < len(df)/10):\n"
    "        if df[col].nunique() > 5 and df[col].nunique() < 100:  # Boa cardinalidade\n"
    "            GROUP_KEY = col; break\n"
    "# Fallback: usar primeira coluna categórica disponível\n"
    "if not GROUP_KEY:\n"
    "    cat_cols = [c for c in df.columns if df[c].dtype == 'object' and df[c].nunique() > 3]\n"
    "    if cat_cols:\n"
    "        GROUP_KEY = cat_cols[0]\n"
    "    else:\n"
    "        # Último fallback: criar grupos sintéticos baseados em percentis de valor\n"
    "        try:\n"
    "            df['grupo_sintetico'] = pd.qcut(df['valor'], q=10, labels=False, duplicates='drop')\n"
    "            df['grupo_sintetico'] = 'Grupo_' + (df['grupo_sintetico'] + 1).astype(str)\n"
    "        except Exception:\n"
    "            # Se qcut falhar, usar binning simples\n"
    "            df['grupo_sintetico'] = pd.cut(df['valor'], bins=10, labels=False)\n"
    "            df['grupo_sintetico'] = 'Grupo_' + (df['grupo_sintetico'] + 1).astype(str)\n"
    "        GROUP_KEY = 'grupo_sintetico'\n"
    "print('Chave de agregação selecionada:', GROUP_KEY, f'(cardinalidade: {df[GROUP_KEY].nunique()})')\n"
))

nb.cells.append(code(
    "# Agregações regionais para análise\n"
    "agg_dict = {\n"
    "    'valor': ['sum','mean','median','count','std'],\n"
    "}\n"
    "# Adicionar outras métricas se disponíveis\n"
    "for col in ['qtd', 'Preco_Custo', 'Desconto']:\n"
    "    if col in df.columns:\n"
    "        agg_dict[col] = ['sum','mean']\n"
    "# Realizar agregação\n"
    "regional_data = df.groupby(GROUP_KEY).agg(agg_dict)\n"
    "regional_data.columns = ['_'.join([a,b]) for a,b in regional_data.columns]\n"
    "regional_data = regional_data.reset_index()\n"
    "print('Dados regionais agregados:', regional_data.shape)\n"
    "# Adicionar coordenadas médias se disponíveis\n"
    "if {'latitude','longitude'}.issubset(df.columns):\n"
    "    coords = df.groupby(GROUP_KEY)[['latitude','longitude']].mean().reset_index()\n"
    "    regional_data = regional_data.merge(coords, on=GROUP_KEY, how='left')\n"
    "regional_data.head()\n"
))

# 2. Mapa exploratório
nb.cells.append(md('### 1.1 Mapa Exploratório de Vendas'))
nb.cells.append(code(
    "# Mapa de vendas por região usando folium\n"
    "try:\n"
    "    import folium\n"
    "    center = [-14.2350, -51.9253]  # Centro do Brasil\n"
    "    m_exploratory = folium.Map(location=center, zoom_start=4)\n"
    "    if {'latitude','longitude'}.issubset(regional_data.columns):\n"
    "        for _, row in regional_data.iterrows():\n"
    "            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):\n"
    "                receita = row.get('valor_sum', 0)\n"
    "                radius = max(5, min(20, receita/10000))  # Escala do círculo\n"
    "                folium.CircleMarker(\n"
    "                    [row['latitude'], row['longitude']],\n"
    "                    radius=radius,\n"
    "                    color='blue',\n"
    "                    fill=True,\n"
    "                    popup=f\"{row[GROUP_KEY]}: R$ {receita:,.0f}\"\n"
    "                ).add_to(m_exploratory)\n"
    "        m_exploratory.save(str(REPORTS/'mapa_exploratorio.html'))\n"
    "        print('Mapa exploratório salvo em:', REPORTS/'mapa_exploratorio.html')\n"
    "    else:\n"
    "        print('Coordenadas não disponíveis para mapeamento')\n"
    "except ImportError:\n"
    "    print('Folium não disponível - pulando mapeamento')\n"
))

# 3. Preparação de features
nb.cells.append(md('### 1.2 Preparação de Features para Modelagem'))
nb.cells.append(code(
    "# Selecionar features numéricas para modelagem\n"
    "feature_cols = [c for c in regional_data.columns if c != GROUP_KEY and pd.api.types.is_numeric_dtype(regional_data[c])]\n"
    "# Remover coordenadas das features (usar apenas para visualização)\n"
    "feature_cols = [c for c in feature_cols if c not in ['latitude','longitude']]\n"
    "# Preparar matriz de features\n"
    "X_raw = regional_data[feature_cols].fillna(regional_data[feature_cols].median())\n"
    "# Definir target para abordagem supervisionada\n"
    "if 'valor_sum' in regional_data.columns:\n"
    "    y_raw = regional_data['valor_sum'].values\n"
    "    X_features = X_raw.drop(columns=['valor_sum'], errors='ignore')\n"
    "else:\n"
    "    # Fallback: usar primeira coluna numérica como proxy\n"
    "    y_raw = X_raw.iloc[:,0].values\n"
    "    X_features = X_raw.iloc[:,1:]\n"
    "print('Features para modelagem:', X_features.columns.tolist())\n"
    "print('Target (receita):', f'min={y_raw.min():.0f}, max={y_raw.max():.0f}, mean={y_raw.mean():.0f}')\n"
))

# 4. Abordagem Supervisionada
nb.cells.append(md('## 2. Abordagem Supervisionada - Modelo Preditivo'))
nb.cells.append(code(
    "# Normalizar features para modelagem\n"
    "scaler_sup = StandardScaler()\n"
    "X_scaled = scaler_sup.fit_transform(X_features)\n"
    "# Configurar modelo Random Forest\n"
    "rf_model = RandomForestRegressor(\n"
    "    n_estimators=400,\n"
    "    max_depth=10,\n"
    "    min_samples_split=5,\n"
    "    random_state=42,\n"
    "    n_jobs=-1\n"
    ")\n"
    "# Validação cruzada 5-fold\n"
    "cv = KFold(n_splits=5, shuffle=True, random_state=42)\n"
    "scoring = {'rmse':'neg_root_mean_squared_error','mae':'neg_mean_absolute_error','r2':'r2'}\n"
    "cv_results = cross_validate(rf_model, X_scaled, y_raw, cv=cv, scoring=scoring, n_jobs=1)\n"
    "# Compilar métricas\n"
    "metrics_sup = {}\n"
    "for metric, scores in cv_results.items():\n"
    "    if metric.startswith('test_'):\n"
    "        name = metric.replace('test_', '')\n"
    "        values = -scores if 'neg_' in metric else scores\n"
    "        metrics_sup[name] = {'mean': float(np.mean(values)), 'std': float(np.std(values))}\n"
    "print('Métricas Supervisionadas (CV 5-fold):')\n"
    "for name, stats in metrics_sup.items():\n"
    "    print(f'  {name}: {stats[\"mean\"]:.4f} ± {stats[\"std\"]:.4f}')\n"
))

nb.cells.append(code(
    "# Treinar modelo final e gerar scores 0-10\n"
    "rf_model.fit(X_scaled, y_raw)\n"
    "y_pred = rf_model.predict(X_scaled)\n"
    "# Normalizar para score 0-10\n"
    "score_scaler = MinMaxScaler(feature_range=(0, 10))\n"
    "scores_supervised = score_scaler.fit_transform(y_pred.reshape(-1, 1)).ravel()\n"
    "# Criar ranking supervisionado\n"
    "ranking_sup = regional_data[[GROUP_KEY]].copy()\n"
    "ranking_sup['receita_real'] = y_raw\n"
    "ranking_sup['receita_pred'] = y_pred\n"
    "ranking_sup['score_supervisionado'] = scores_supervised\n"
    "ranking_sup = ranking_sup.sort_values('score_supervisionado', ascending=False).reset_index(drop=True)\n"
    "ranking_sup['rank_supervisionado'] = range(1, len(ranking_sup) + 1)\n"
    "# Salvar ranking\n"
    "ranking_sup.to_csv(REPORTS/'ranking_supervisionado.csv', index=False)\n"
    "print('Top 10 Regiões - Abordagem Supervisionada:')\n"
    "print(ranking_sup[['rank_supervisionado', GROUP_KEY, 'score_supervisionado']].head(10))\n"
))

# 5. Abordagem Não-Supervisionada
nb.cells.append(md('## 3. Abordagem Não-Supervisionada - Segmentação Territorial'))
nb.cells.append(code(
    "# Normalizar features para clustering\n"
    "scaler_unsup = StandardScaler()\n"
    "X_scaled_unsup = scaler_unsup.fit_transform(X_features)\n"
    "# Método do cotovelo + Silhouette para seleção de K\n"
    "k_range = range(3, 9)\n"
    "inertias = []\n"
    "silhouette_scores = []\n"
    "for k in k_range:\n"
    "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n"
    "    labels = kmeans.fit_predict(X_scaled_unsup)\n"
    "    inertias.append(kmeans.inertia_)\n"
    "    sil_score = silhouette_score(X_scaled_unsup, labels)\n"
    "    silhouette_scores.append(sil_score)\n"
    "    print(f'K={k}: Inertia={kmeans.inertia_:.2f}, Silhouette={sil_score:.3f}')\n"
    "# Selecionar melhor K por silhouette\n"
    "best_k = k_range[np.argmax(silhouette_scores)]\n"
    "best_silhouette = max(silhouette_scores)\n"
    "print(f'\\nMelhor K selecionado: {best_k} (Silhouette: {best_silhouette:.3f})')\n"
))

nb.cells.append(code(
    "# Treinar modelo final K-Means\n"
    "kmeans_final = KMeans(n_clusters=best_k, random_state=42, n_init=10)\n"
    "cluster_labels = kmeans_final.fit_predict(X_scaled_unsup)\n"
    "# Analisar clusters\n"
    "cluster_analysis = regional_data.copy()\n"
    "cluster_analysis['cluster'] = cluster_labels\n"
    "# Estatísticas por cluster\n"
    "if 'valor_sum' in cluster_analysis.columns:\n"
    "    agg_dict = {'valor_sum': ['count', 'mean', 'sum']}\n"
    "else:\n"
    "    agg_dict = {GROUP_KEY: 'count'}\n"
    "cluster_stats = cluster_analysis.groupby('cluster').agg(agg_dict).round(2)\n"
    "cluster_stats.columns = ['_'.join(col).strip() for col in cluster_stats.columns]\n"
    "print('Estatísticas por Cluster:')\n"
    "print(cluster_stats)\n"
    "# Criar ranking por cluster (baseado na receita média do cluster)\n"
    "if 'valor_sum' in cluster_analysis.columns:\n"
    "    cluster_performance = cluster_analysis.groupby('cluster')['valor_sum'].mean().sort_values(ascending=False)\n"
    "    cluster_ranking = {cluster: rank+1 for rank, cluster in enumerate(cluster_performance.index)}\n"
    "else:\n"
    "    cluster_ranking = {i: i+1 for i in range(best_k)}\n"
    "cluster_analysis['cluster_rank'] = cluster_analysis['cluster'].map(cluster_ranking)\n"
    "cluster_analysis = cluster_analysis.sort_values(['cluster_rank', 'valor_sum'], ascending=[True, False])\n"
    "# Salvar resultados\n"
    "cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']].to_csv(REPORTS/'clusters_nao_supervisionado.csv', index=False)\n"
    "print('\\nTop 10 Regiões por Cluster (ordenado por performance do cluster):')\n"
    "print(cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']].head(10))\n"
))

# 6. Comparação Estratégica
nb.cells.append(md('## 4. Comparação Estratégica Entre Abordagens'))
nb.cells.append(code(
    "# Merge dos resultados para comparação\n"
    "comparison = ranking_sup[[GROUP_KEY, 'rank_supervisionado', 'score_supervisionado']].merge(\n"
    "    cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']], on=GROUP_KEY, how='inner'\n"
    ")\n"
    "# Análise de concordância\n"
    "comparison['rank_diff'] = abs(comparison['rank_supervisionado'] - comparison['cluster_rank'])\n"
    "comparison['concordancia'] = comparison['rank_diff'] <= 3  # Concordância se diferença <= 3 posições\n"
    "concordancia_rate = comparison['concordancia'].mean()\n"
    "print(f'Taxa de concordância entre métodos: {concordancia_rate:.2%}')\n"
    "# Top regiões por ambos os métodos\n"
    "top_both = comparison[\n"
    "    (comparison['rank_supervisionado'] <= 10) & (comparison['cluster_rank'] <= 3)\n"
    "].sort_values('rank_supervisionado')\n"
    "print('\\nRegiões priorizadas por AMBOS os métodos:')\n"
    "print(top_both[[GROUP_KEY, 'rank_supervisionado', 'cluster_rank', 'score_supervisionado']])\n"
    "# Salvar comparação\n"
    "comparison.to_csv(REPORTS/'comparacao_metodos.csv', index=False)\n"
))

nb.cells.append(code(
    "# Análise das diferenças metodológicas\n"
    "print('=== ANÁLISE COMPARATIVA ===')\n"
    "print('\\nABORDAGEM SUPERVISIONADA:')\n"
    "print('- Foco: Predição de receita baseada em padrões históricos')\n"
    "print('- Vantagem: Quantifica potencial financeiro diretamente')\n"
    "print('- Limitação: Dependente da qualidade dos dados históricos')\n"
    "print('- Uso recomendado: Expansão em mercados similares aos existentes')\n"
    "print('\\nABORDAGEM NÃO-SUPERVISIONADA:')\n"
    "print('- Foco: Identificação de padrões e segmentos territoriais')\n"
    "print('- Vantagem: Descobre grupos não óbvios, útil para estratégias diferenciadas')\n"
    "print('- Limitação: Não quantifica diretamente o potencial financeiro')\n"
    "print('- Uso recomendado: Exploração de novos mercados, estratégias por segmento')\n"
    "print('\\nCONCORDÂNCIA:')\n"
    "print(f'- {concordancia_rate:.1%} das regiões têm ranking similar entre métodos')\n"
    "print(f'- {len(top_both)} regiões são priorizadas por ambos os métodos')\n"
))

# 7. Dashboard Interativo
nb.cells.append(md('## 5. Dashboard Interativo e Visualizações'))
nb.cells.append(code(
    "# Mapa interativo completo\n"
    "try:\n"
    "    import folium\n"
    "    from folium import plugins\n"
    "    # Mapa base\n"
    "    center = [-14.2350, -51.9253]\n"
    "    m_complete = folium.Map(location=center, zoom_start=4)\n"
    "    # Cores para clusters\n"
    "    cluster_colors = ['red', 'blue', 'green', 'purple', 'orange', 'darkred', 'lightred', 'beige']\n"
    "    if {'latitude','longitude'}.issubset(regional_data.columns):\n"
    "        # Merge dados completos\n"
    "        map_data = regional_data.merge(comparison, on=GROUP_KEY, how='left')\n"
    "        for _, row in map_data.iterrows():\n"
    "            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):\n"
    "                # Tamanho baseado no score supervisionado\n"
    "                radius = max(5, min(15, row.get('score_supervisionado', 5)))\n"
    "                # Cor baseada no cluster\n"
    "                cluster_idx = int(row.get('cluster', 0)) % len(cluster_colors)\n"
    "                color = cluster_colors[cluster_idx]\n"
    "                # Popup informativo\n"
    "                popup_text = f\"\"\"\n"
    "                <b>{row[GROUP_KEY]}</b><br>\n"
    "                Score Supervisionado: {row.get('score_supervisionado', 'N/A'):.1f}<br>\n"
    "                Cluster: {row.get('cluster', 'N/A')}<br>\n"
    "                Rank Supervisionado: {row.get('rank_supervisionado', 'N/A')}<br>\n"
    "                Rank Cluster: {row.get('cluster_rank', 'N/A')}\n"
    "                \"\"\"\n"
    "                folium.CircleMarker(\n"
    "                    [row['latitude'], row['longitude']],\n"
    "                    radius=radius,\n"
    "                    color=color,\n"
    "                    fill=True,\n"
    "                    popup=folium.Popup(popup_text, max_width=300)\n"
    "                ).add_to(m_complete)\n"
    "        # Adicionar legenda\n"
    "        legend_html = '<div style=\"position: fixed; top: 10px; right: 10px; z-index:1000; background-color:white; padding:10px; border:2px solid grey;\">'\n"
    "        legend_html += '<h4>Legenda</h4>'\n"
    "        legend_html += '<p><b>Tamanho:</b> Score Supervisionado</p>'\n"
    "        legend_html += '<p><b>Cor:</b> Cluster Não-Supervisionado</p>'\n"
    "        legend_html += '</div>'\n"
    "        m_complete.get_root().html.add_child(folium.Element(legend_html))\n"
    "        m_complete.save(str(REPORTS/'mapa_interativo_completo.html'))\n"
    "        print('Mapa interativo completo salvo em:', REPORTS/'mapa_interativo_completo.html')\n"
    "    else:\n"
    "        print('Coordenadas não disponíveis para mapeamento completo')\n"
    "except ImportError:\n"
    "    print('Folium não disponível - pulando mapeamento interativo')\n"
))

# 8. Recomendações Executivas
nb.cells.append(md('## 6. Recomendações Executivas'))
nb.cells.append(code(
    "# Síntese dos resultados\n"
    "print('=== SÍNTESE EXECUTIVA ===')\n"
    "print(f'\\nTotal de regiões analisadas: {len(regional_data)}')\n"
    "print(f'Clusters identificados: {best_k}')\n"
    "print(f'Taxa de concordância entre métodos: {concordancia_rate:.1%}')\n"
    "# Top 5 integrado (priorizando concordância)\n"
    "top5_integrated = comparison.copy()\n"
    "# Score integrado: média ponderada (70% supervisionado, 30% cluster)\n"
    "top5_integrated['score_integrado'] = (\n"
    "    0.7 * (11 - top5_integrated['rank_supervisionado']) / 10 +  # Normalizar rank\n"
    "    0.3 * (best_k + 1 - top5_integrated['cluster_rank']) / best_k\n"
    ")\n"
    "top5_integrated = top5_integrated.sort_values('score_integrado', ascending=False)\n"
    "print('\\n=== TOP 5 REGIÕES INTEGRADAS ===')\n"
    "for i, (_, row) in enumerate(top5_integrated.head(5).iterrows(), 1):\n"
    "    print(f'{i}. {row[GROUP_KEY]}')\n"
    "    print(f'   Score Supervisionado: {row[\"score_supervisionado\"]:.1f} (Rank: {row[\"rank_supervisionado\"]})')\n"
    "    print(f'   Cluster: {row[\"cluster\"]} (Rank: {row[\"cluster_rank\"]})')\n"
    "    print(f'   Score Integrado: {row[\"score_integrado\"]:.3f}')\n"
    "    print()\n"
))

nb.cells.append(code(
    "# Recomendações específicas por método\n"
    "recommendations = []\n"
    "# Top 5 supervisionado\n"
    "for i, (_, row) in enumerate(ranking_sup.head(5).iterrows(), 1):\n"
    "    recommendations.append({\n"
    "        'rank': i,\n"
    "        'regiao': row[GROUP_KEY],\n"
    "        'metodo': 'Supervisionado',\n"
    "        'score': row['score_supervisionado'],\n"
    "        'justificativa': f'Alto potencial de receita predito (R$ {row[\"receita_pred\"]:,.0f})',\n"
    "        'acao_recomendada': 'Expansão prioritária - ROI esperado alto'\n"
    "    })\n"
    "# Top clusters\n"
    "cluster_recs = cluster_analysis.groupby('cluster').first().sort_values('cluster_rank')\n"
    "for i, (cluster, row) in enumerate(cluster_recs.head(3).iterrows(), 1):\n"
    "    recommendations.append({\n"
    "        'rank': i,\n"
    "        'regiao': f'Cluster {cluster} (ex: {row[GROUP_KEY]})',\n"
    "        'metodo': 'Não-Supervisionado',\n"
    "        'score': f'Cluster {cluster}',\n"
    "        'justificativa': f'Segmento territorial de alto potencial',\n"
    "        'acao_recomendada': f'Estratégia diferenciada para cluster {cluster}'\n"
    "    })\n"
    "# Salvar recomendações\n"
    "rec_df = pd.DataFrame(recommendations)\n"
    "rec_df.to_csv(REPORTS/'recomendacoes_executivas.csv', index=False)\n"
    "print('Recomendações executivas salvas em:', REPORTS/'recomendacoes_executivas.csv')\n"
))

# 9. Persistência dos modelos
nb.cells.append(md('### 6.1 Persistência dos Modelos'))
nb.cells.append(code(
    "# Salvar modelos treinados\n"
    "supervised_model = {\n"
    "    'model': rf_model,\n"
    "    'scaler': scaler_sup,\n"
    "    'score_scaler': score_scaler,\n"
    "    'features': X_features.columns.tolist(),\n"
    "    'metrics': metrics_sup,\n"
    "    'group_key': GROUP_KEY\n"
    "}\n"
    "unsupervised_model = {\n"
    "    'model': kmeans_final,\n"
    "    'scaler': scaler_unsup,\n"
    "    'features': X_features.columns.tolist(),\n"
    "    'best_k': best_k,\n"
    "    'silhouette_score': best_silhouette,\n"
    "    'group_key': GROUP_KEY\n"
    "}\n"
    "joblib.dump(supervised_model, MODELS/'territorial_supervised.pkl')\n"
    "joblib.dump(unsupervised_model, MODELS/'territorial_unsupervised.pkl')\n"
    "print('Modelos salvos em:')\n"
    "print('- Supervisionado:', MODELS/'territorial_supervised.pkl')\n"
    "print('- Não-supervisionado:', MODELS/'territorial_unsupervised.pkl')\n"
))

# 10. Resumo final
nb.cells.append(md('## 7. Resumo Final e Próximos Passos'))
nb.cells.append(code(
    "print('=== RESUMO FINAL ===')\n"
    "print('\\n✅ ENTREGÁVEIS GERADOS:')\n"
    "print('- Ranking supervisionado:', REPORTS/'ranking_supervisionado.csv')\n"
    "print('- Clusters não-supervisionados:', REPORTS/'clusters_nao_supervisionado.csv')\n"
    "print('- Comparação de métodos:', REPORTS/'comparacao_metodos.csv')\n"
    "print('- Mapa interativo:', REPORTS/'mapa_interativo_completo.html')\n"
    "print('- Recomendações executivas:', REPORTS/'recomendacoes_executivas.csv')\n"
    "print('- Modelos treinados em models/')\n"
    "print('\\n🎯 PRÓXIMOS PASSOS:')\n"
    "print('1. Validar recomendações com equipe comercial')\n"
    "print('2. Coletar dados externos (demografia, concorrência) para enriquecer análise')\n"
    "print('3. Implementar monitoramento de performance das regiões priorizadas')\n"
    "print('4. Desenvolver dashboard executivo para acompanhamento contínuo')\n"
    "print('\\n📊 CRITÉRIOS DE ESCOLHA DO MÉTODO:')\n"
    "print('- Use SUPERVISIONADO quando: foco em ROI, mercados similares aos existentes')\n"
    "print('- Use NÃO-SUPERVISIONADO quando: exploração de novos mercados, estratégias diferenciadas')\n"
    "print('- Use AMBOS quando: decisões críticas que requerem múltiplas perspectivas')\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook built at', NB_PATH)
