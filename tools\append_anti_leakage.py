from pathlib import Path
import nbformat as nbf

BASE = Path('.')
NB_PATH = BASE / 'notebooks' / '04_territorial_analysis.ipynb'

extra_code = r'''# Anti-leakage & advanced visuals (learning curves, residuals, CIs)
import re, math
from sklearn.model_selection import learning_curve, KFold
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error

# Detect target-derived columns to exclude
base_targets = ['valor','revenue','sales','y']
if 'target' in globals() and target is not None:
    base_targets = list(dict.fromkeys([target, *base_targets]))

def _is_leak(col: str) -> bool:
    c = col.lower()
    pats = []
    for t in base_targets:
        t = str(t).lower()
        pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in c for p in pats)

clean_num_cols = [c for c in num_cols if not _is_leak(c)] if 'num_cols' in globals() else []
if target in clean_num_cols:
    clean_num_cols = [c for c in clean_num_cols if c != target]
if not clean_num_cols and 'num_cols' in globals():
    clean_num_cols = [c for c in num_cols if c != target]
print('Clean feature columns:', len(clean_num_cols))

# Recompute metrics with bootstrap CIs
import numpy as np, pandas as pd, matplotlib.pyplot as plt, seaborn as sns
from sklearn.model_selection import train_test_split

metrics_rows2 = []
all_preds2 = []
clusters = sorted(pd.Series(cluster_labels).dropna().unique()) if 'cluster_labels' in globals() else []
for c_id in clusters:
    mask = (pd.Series(cluster_labels)==c_id).values
    dfc = df.loc[mask]
    if len(dfc) < 120:
        continue
    X = dfc[clean_num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)
    Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)
    pipe = make_model(str(best_model_name))
    pipe.fit(Xtr, ytr)
    yp = pipe.predict(Xte)
    r2 = float(r2_score(yte, yp)); rmse = float(np.sqrt(mean_squared_error(yte, yp))); mae = float(mean_absolute_error(yte, yp))
    # Bootstrap CIs
    rng = np.random.RandomState(42)
    R2s=[]; RMSEs=[]; MAEs=[]
    n=len(yte)
    for b in range(200):
        idx = rng.choice(n, n, replace=True)
        yt = np.array(yte)[idx]; yp_b = np.array(yp)[idx]
        with np.errstate(all='ignore'):
            R2s.append(r2_score(yt, yp_b))
        RMSEs.append(float(np.sqrt(mean_squared_error(yt, yp_b))))
        MAEs.append(float(mean_absolute_error(yt, yp_b)))
    def ci(a):
        lo, hi = np.percentile(a, [2.5, 97.5])
        return float(lo), float(hi)
    r2_lo, r2_hi = ci(R2s); rmse_lo, rmse_hi = ci(RMSEs); mae_lo, mae_hi = ci(MAEs)
    row = {'cluster':int(c_id),'n':int(len(dfc)), 'r2':r2,'r2_lo':r2_lo,'r2_hi':r2_hi,
           'rmse':rmse,'rmse_lo':rmse_lo,'rmse_hi':rmse_hi,
           'mae':mae,'mae_lo':mae_lo,'mae_hi':mae_hi}
    metrics_rows2.append(row)
    all_preds2.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))

if metrics_rows2:
    mdf2 = pd.DataFrame(metrics_rows2).sort_values('cluster')
    (Path(REPORTS)/'tables'/'supervised_by_cluster_metrics_ci.csv').parent.mkdir(parents=True, exist_ok=True)
    mdf2.to_csv(Path(REPORTS)/'tables'/'supervised_by_cluster_metrics_ci.csv', index=False)
    # Barplots with error bars
    fig, axes = plt.subplots(1,3, figsize=(15,4))
    axes[0].bar(mdf2['cluster'], mdf2['r2'], yerr=[mdf2['r2']-mdf2['r2_lo'], mdf2['r2_hi']-mdf2['r2']], capsize=4)
    axes[0].set_title('R² por Cluster (95% CI)'); axes[0].set_xlabel('Cluster'); axes[0].set_ylabel('R²')
    axes[1].bar(mdf2['cluster'], mdf2['rmse'], yerr=[mdf2['rmse']-mdf2['rmse_lo'], mdf2['rmse_hi']-mdf2['rmse']], capsize=4)
    axes[1].set_title('RMSE por Cluster (95% CI)'); axes[1].set_xlabel('Cluster')
    axes[2].bar(mdf2['cluster'], mdf2['mae'], yerr=[mdf2['mae']-mdf2['mae_lo'], mdf2['mae_hi']-mdf2['mae']], capsize=4)
    axes[2].set_title('MAE por Cluster (95% CI)'); axes[2].set_xlabel('Cluster')
    plt.tight_layout(); plt.savefig(Path(REPORTS)/'plots'/'supervised_by_cluster_performance.png', dpi=180); plt.show()

if all_preds2:
    preds2 = pd.concat(all_preds2, ignore_index=True)
    preds2.to_csv(Path(REPORTS)/'tables'/'supervised_by_cluster_predictions.csv', index=False)
    plt.figure(figsize=(6,6));
    sns.scatterplot(data=preds2, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)
    lim = (min(preds2['y_true'].min(), preds2['y_pred'].min()), max(preds2['y_true'].max(), preds2['y_pred'].max()))
    plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(Path(REPORTS)/'plots'/'supervised_by_cluster_predictions.png', dpi=180); plt.show()

# Learning curves per cluster
try:
    for c_id in clusters:
        dfc = df.loc[(pd.Series(cluster_labels)==c_id).values]
        if len(dfc) < 200: continue
        X = dfc[clean_num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)
        est = make_model(str(best_model_name))
        cv = KFold(n_splits=5, shuffle=True, random_state=42)
        train_sizes, train_scores, val_scores = learning_curve(est, X, y, cv=cv, scoring='r2', train_sizes=np.linspace(0.2, 1.0, 5))
        tr_mean, tr_std = train_scores.mean(axis=1), train_scores.std(axis=1)
        va_mean, va_std = val_scores.mean(axis=1), val_scores.std(axis=1)
        plt.figure(figsize=(6,4))
        plt.plot(train_sizes, tr_mean, 'o-', label='Treino'); plt.fill_between(train_sizes, tr_mean-tr_std, tr_mean+tr_std, alpha=0.2)
        plt.plot(train_sizes, va_mean, 'o-', label='Validação'); plt.fill_between(train_sizes, va_mean-va_std, va_mean+va_std, alpha=0.2)
        plt.title(f'Learning Curve — Cluster {int(c_id)}'); plt.xlabel('Amostras de treino'); plt.ylabel('R²'); plt.legend(); plt.tight_layout()
        plt.savefig(Path(REPORTS)/'plots'/f'learning_curve_cluster_{int(c_id)}.png', dpi=180); plt.show()
except Exception as e:
    print('Learning curves skipped ->', e)

# Residual analysis per cluster
try:
    for c_id in clusters:
        dfc = df.loc[(pd.Series(cluster_labels)==c_id).values]
        if len(dfc) < 120: continue
        X = dfc[clean_num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)
        from sklearn.model_selection import train_test_split
        est = make_model(str(best_model_name))
        Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)
        est.fit(Xtr, ytr)
        yp = est.predict(Xte)
        res = yte.values - yp
        plt.figure(figsize=(6,4)); sns.histplot(res, kde=True); plt.title(f'Residuals — Cluster {int(c_id)}'); plt.tight_layout(); plt.savefig(Path(REPORTS)/'plots'/f'residuals_hist_cluster_{int(c_id)}.png', dpi=180); plt.show()
        plt.figure(figsize=(6,4)); plt.scatter(yp, res, s=10, alpha=0.6); plt.axhline(0,color='k',lw=1); plt.title(f'Residuals vs Fitted — Cluster {int(c_id)}'); plt.xlabel('Previsto'); plt.ylabel('Resíduo'); plt.tight_layout(); plt.savefig(Path(REPORTS)/'plots'/f'residuals_vs_fitted_cluster_{int(c_id)}.png', dpi=180); plt.show()
except Exception as e:
    print('Residuals plots skipped ->', e)
'''

def main():
    nb = nbf.read(NB_PATH, as_version=4)
    nb.cells.append(nbf.v4.new_code_cell(extra_code))
    nbf.write(nb, NB_PATH)
    print('Anti-leakage cell appended')

if __name__ == '__main__':
    main()

