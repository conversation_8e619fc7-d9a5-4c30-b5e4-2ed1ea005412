from pathlib import Path
import nbformat as nbf

BASE = Path('.')
NB04 = BASE / 'notebooks' / '04_territorial_analysis.ipynb'
NB05 = BASE / 'notebooks' / '05_customer_segmentation.ipynb'

anti_leak_05 = r'''# Anti-leakage for clustering: exclude target and derived columns
import pandas as pd, numpy as np
from pathlib import Path
BASE = Path('.')
if not (BASE/'data'/'processed'/'features_engineered_regional.csv').exists(): BASE = Path('..')
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv' if (BASE/'data'/'processed'/'features_engineered_regional.csv').exists() else BASE/'data'/'processed'/'features_engineered.csv'
df = pd.read_csv(DATA, low_memory=False)

# Build list of numeric features excluding target and derivations
candidates_target = ['valor','revenue','sales','y']
true_target = None
for t in candidates_target:
    if t in df.columns: true_target = t; break
num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]

def is_leak(c:str)->bool:
    if true_target is None: return False
    c=c.lower(); t=true_target.lower()
    pats=[t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in c for p in pats)
num_clean = [c for c in num_cols if not is_leak(c)]
# Use territorial/contextual cols for clustering preference
territorial = []
if 'UF' in df.columns: territorial.append('UF')
territorial += [c for c in df.columns if 'REGIAO_CHILLI' in c]
# Persist selections for downstream notebooks
from pathlib import Path
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'; TABLES.mkdir(parents=True, exist_ok=True)
pd.Series(num_clean, name='num_clean').to_csv(TABLES/'clustering_numeric_clean_columns.csv', index=False)
pd.Series(territorial, name='territorial_cols').to_csv(TABLES/'clustering_territorial_columns.csv', index=False)
print('Saved clean feature lists for clustering:', len(num_clean), 'numeric; territorial=', territorial)
'''

map_cell_04 = r'''# Brazil map overlay for territorial clusters using plotly
import json, requests
import numpy as np, pandas as pd
import plotly.express as px
from pathlib import Path

# Determine dominant cluster per UF
df_tmp = df.copy()
df_tmp['cluster'] = pd.Series(cluster_labels, index=df_tmp.index)
uf_series = None
if 'UF' in df_tmp.columns:
    uf_series = df_tmp['UF'].astype(str)
else:
    # try infer UF from one-hot columns like UF_SP, else collapse to REGIAO_CHILLI
    oh_uf = [c for c in df_tmp.columns if c.startswith('UF_')]
    if oh_uf:
        idx = np.argmax(df_tmp[oh_uf].values, axis=1)
        uf_series = pd.Series([oh_uf[i].replace('UF_','') for i in idx], index=df_tmp.index)
    else:
        reg_cols = [c for c in df_tmp.columns if 'REGIAO_CHILLI' in c]
        if reg_cols:
            idx = np.argmax(df_tmp[reg_cols].values, axis=1)
            uf_series = pd.Series([reg_cols[i] for i in idx], index=df_tmp.index)

if uf_series is None:
    print('UF not available; skipping map overlay, keeping strip fallback')
else:
    dom = (pd.DataFrame({'UF': uf_series, 'cluster': df_tmp['cluster']})
             .groupby('UF')['cluster']
             .agg(lambda s: s.value_counts().idxmax())
             .reset_index())
    sizes = uf_series.value_counts().rename('count').reset_index().rename(columns={'index':'UF'})
    dom = dom.merge(sizes, on='UF', how='left')

    # GeoJSON of Brazilian states
    # Using public source from IBGE or GitHub mirrors. Here we try a common mirror.
    urls = [
        'https://raw.githubusercontent.com/codeforamerica/click_that_hood/master/public/data/brazil-states.geojson',
        'https://raw.githubusercontent.com/tbrugz/geodata-br/master/geojson/geojs-100-mun.json'
    ]
    gj = None
    for u in urls:
        try:
            gj = requests.get(u, timeout=20).json()
            break
        except Exception:
            continue
    if gj is None:
        print('GeoJSON fetch failed; exporting HTML fallback without basemap')
    
    # Normalize UF key (some geojsons use state names; we handle simple match on UF abbrev if present)
    # Attempt to map state names to UF codes (simplified list)
    name_to_uf = {
        'Acre':'AC','Alagoas':'AL','Amapá':'AP','Amazonas':'AM','Bahia':'BA','Ceará':'CE','Distrito Federal':'DF',
        'Espírito Santo':'ES','Goiás':'GO','Maranhão':'MA','Mato Grosso':'MT','Mato Grosso do Sul':'MS','Minas Gerais':'MG',
        'Pará':'PA','Paraíba':'PB','Paraná':'PR','Pernambuco':'PE','Piauí':'PI','Rio de Janeiro':'RJ','Rio Grande do Norte':'RN',
        'Rio Grande do Sul':'RS','Rondônia':'RO','Roraima':'RR','Santa Catarina':'SC','São Paulo':'SP','Sergipe':'SE','Tocantins':'TO'}

    if gj and 'features' in gj:
        # build DataFrame from GeoJSON
        feats = gj['features']
        gdf = []
        for f in feats:
            props = f.get('properties', {})
            st = props.get('name') or props.get('NM_ESTADO') or props.get('estado') or ''
            uf = props.get('sigla') or name_to_uf.get(st, None)
            if uf:
                gdf.append({'UF': uf})
        gdf = pd.DataFrame(gdf).drop_duplicates()
        plot = dom.merge(gdf, on='UF', how='inner') if not gdf.empty else dom.copy()
    else:
        plot = dom.copy()

    fig = px.choropleth(
        plot, geojson=gj, locations='UF', color='cluster', featureidkey='properties.sigla' if gj else None,
        color_continuous_scale=px.colors.qualitative.Set1 if hasattr(px.colors, 'qualitative') else 'Viridis',
        hover_data={'UF':True, 'cluster':True, 'count':True},
        locationmode=None
    )
    fig.update_geos(fitbounds="locations", visible=False)
    fig.update_layout(margin=dict(l=0,r=0,t=30,b=0), title='Clusters territoriais por UF (dominante)')

    out_png = Path('reports')/'2025-08-15'/'plots'/'territorial_clustering_map.png'
    out_html = Path('reports')/'2025-08-15'/'plots'/'territorial_clustering_map.html'
    out_png.parent.mkdir(parents=True, exist_ok=True)
    try:
        fig.write_image(str(out_png), scale=2)
        print('Saved PNG map to', out_png)
    except Exception as e:
        print('PNG export failed ->', e, '; saving HTML fallback')
        fig.write_html(str(out_html))
'''

anti_leak_04_header = r'''# Ensure clean feature filtering for supervised per-cluster training
# (target and derived must be excluded)
# num_cols must exist; rebuild clean list here too
import pandas as pd
base_targets = ['valor','revenue','sales','y']
if target is not None and target not in base_targets:
    base_targets = [target] + base_targets

def _is_leak(c: str) -> bool:
    c=c.lower()
    pats=[]
    for t in base_targets:
        t=t.lower(); pats+= [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in c for p in pats)

num_cols = [c for c in num_cols if not _is_leak(c)]
'''

def append_cell(path: Path, code: str, cell_type='code'):
    nb = nbf.read(path, as_version=4)
    if cell_type=='code':
        nb.cells.append(nbf.v4.new_code_cell(code))
    else:
        nb.cells.append(nbf.v4.new_markdown_cell(code))
    nbf.write(nb, path)

if __name__ == '__main__':
    # Patch 05: Anti-leakage lists for clustering
    if NB05.exists():
        append_cell(NB05, anti_leak_05, 'code')
        print('Patched 05 with anti-leakage cell')
    else:
        print('05 notebook not found')
    # Patch 04: Ensure clean feature filter header before training
    if NB04.exists():
        append_cell(NB04, anti_leak_04_header, 'code')
        append_cell(NB04, map_cell_04, 'code')
        print('Patched 04 with clean filter header + map cell')
    else:
        print('04 notebook not found')

