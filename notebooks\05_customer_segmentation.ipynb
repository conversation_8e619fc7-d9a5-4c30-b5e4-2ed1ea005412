{"cells": [{"cell_type": "markdown", "metadata": {}, "source": "# 05 - Customer Segmentation\\n\\nThis notebook performs customer clustering and behavioral analysis.\\n"}, {"cell_type": "code", "metadata": {}, "source": "# Setup paths\\nfrom pathlib import Path\\nBASE = Path('.')\\nREPORTS = BASE / 'reports' / '2025-08-15'\\n(REPORTS/ 'tables').mkdir(parents=True, exist_ok=True)\\n(REPORTS/ 'plots').mkdir(parents=True, exist_ok=True)\\n(REPORTS/ 'models').mkdir(parents=True, exist_ok=True)\\nprint('Reports dir:', REPORTS)"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}