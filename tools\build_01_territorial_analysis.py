import nbformat as nbf
from pathlib import Path

md = lambda t: nbf.v4.new_markdown_cell(t)
code = lambda t: nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / '01_territorial_analysis.ipynb'

nb = nbf.v4.new_notebook()

nb.cells.append(md(
    "# Territorial Analysis – ChilliAnalyzer MVP (Vanessa)\n\n"
    "Objetivo: priorizar regiões para expansão de lojas de rua com um score de potencial (0–10).\n\n"
    "Este notebook carrega dados processados, constrói features regionais e treina um RandomForestRegressor para ranquear regiões."
))

# 1. Setup
nb.cells.append(md('## 1. Setup'))
nb.cells.append(code(
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np, json\n"
    "import matplotlib.pyplot as plt, seaborn as sns\n"
    "from sklearn.ensemble import RandomForestRegressor\n"
    "from sklearn.preprocessing import MinMaxScaler\n"
    "from sklearn.model_selection import KFold, cross_validate\n"
    "import warnings; warnings.filterwarnings('ignore')\n"
    "BASE = Path('.')\n"
    "if not (BASE/'data'/'processed'/'features_engineered.csv').exists(): BASE = Path('..')\n"
    "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n"
    "REPORTS = BASE/'reports'/'2025-08-15'/'territorial_analysis'\n"
    "MODELS = BASE/'models'\n"
    "for d in [REPORTS, MODELS]: d.mkdir(parents=True, exist_ok=True)\n"
    "print('DATA:', DATA)\n"
))

# 2. Load and region preparation
nb.cells.append(md('## 2. Carregamento e seleção de colunas de região'))
nb.cells.append(code(
    "df = pd.read_csv(DATA, parse_dates=['data'], low_memory=False)\n"
    "# Colunas potenciais de localização/segmento regional\n"
    "cands = ['regiao','uf','cidade','bairro','latitude','longitude','Tipo_PDV']\n"
    "present = [c for c in cands if c in df.columns]\n"
    "print('Colunas regionais disponíveis:', present)\n"
    "if not present:\n"
    "    raise SystemExit('Nenhuma coluna regional disponível para análise territorial.')\n"
    "# Chave de agregação (prioridade: cidade->uf->regiao->Tipo_PDV)\n"
    "for key in ['cidade','uf','regiao','Tipo_PDV']:\n"
    "    if key in present:\n"
    "        GROUP_KEY = key; break\n"
    "print('GROUP_KEY:', GROUP_KEY)\n"
))

# 3. Feature engineering regional
nb.cells.append(md('## 3. Features regionais e alvo'))
nb.cells.append(code(
    "# Agregações regionais\n"
    "aggs = {\n"
    "    'valor': ['sum','mean','median','count'],\n"
    "    'qtd': ['sum','mean'] if 'qtd' in df.columns else [],\n"
    "}\n"
    "# Filtra colunas existentes\n"
    "aggs = {k:[m for m in v if m] for k,v in aggs.items() if k in df.columns}\n"
    "grp = df.groupby(GROUP_KEY).agg(aggs)\n"
    "grp.columns = ['_'.join([a,b]) for a,b in grp.columns] if isinstance(grp.columns, pd.MultiIndex) else grp.columns\n"
    "grp = grp.reset_index()\n"
    "# Placeholders para variáveis externas (se existirem)\n"
    "for ext in ['densidade_pop','renda_media','concorrencia']:\n"
    "    if ext not in grp.columns: grp[ext] = np.nan\n"
    "# Define alvo como valor_sum (receita total) e normaliza para score 0-10\n"
    "if 'valor_sum' not in grp.columns: raise SystemExit('Coluna valor_sum ausente para alvo.')\n"
    "y = grp['valor_sum'].values\n"
    "X = grp.drop(columns=['valor_sum'])\n"
    "num_cols = [c for c in X.columns if pd.api.types.is_numeric_dtype(X[c])]\n"
    "Xnum = X[num_cols].fillna(X[num_cols].median())\n"
    "print('X shape:', Xnum.shape)\n"
))

# 4. Modelo e validação
nb.cells.append(md('## 4. Treinamento do modelo e validação'))
nb.cells.append(code(
    "seed=42\n"
    "rf = RandomForestRegressor(n_estimators=400, random_state=seed, n_jobs=-1)\n"
    "cv = KFold(n_splits=5, shuffle=True, random_state=seed)\n"
    "scoring = {'rmse':'neg_root_mean_squared_error','mae':'neg_mean_absolute_error','r2':'r2'}\n"
    "cvres = cross_validate(rf, Xnum, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n"
    "import numpy as np\n"
    "metrics = {k: (float(np.mean(v)), float(np.std(v))) for k,v in cvres.items() if k.startswith('test_')}\n"
    "metrics\n"
))

# 5. Fit final e score 0-10
nb.cells.append(md('## 5. Ajuste final e ranqueamento'))
nb.cells.append(code(
    "rf.fit(Xnum, y)\n"
    "pred = rf.predict(Xnum)\n"
    "scaler = MinMaxScaler(feature_range=(0,10))\n"
    "score = scaler.fit_transform(pred.reshape(-1,1)).ravel()\n"
    "out = grp[[GROUP_KEY]].copy(); out['score_potencial'] = score\n"
    "rank = out.sort_values('score_potencial', ascending=False).reset_index(drop=True)\n"
    "rank_path = REPORTS/'ranking_regioes.csv'\n"
    "rank.to_csv(rank_path, index=False)\n"
    "print('Ranking salvo em', rank_path)\n"
))

# 6. Mapa interativo (folium)
nb.cells.append(md('## 6. Mapa interativo (folium)'))
nb.cells.append(code(
    "try:\n"
    "    import folium\n"
    "    # Centro aproximado\n"
    "    center = [ -14.2350, -51.9253 ]\n"
    "    fmap = folium.Map(location=center, zoom_start=4)\n"
    "    # Se tivermos lat/long por linha original, agregar média da região\n"
    "    if {'latitude','longitude'}.issubset(df.columns):\n"
    "        coords = df.groupby(GROUP_KEY)[['latitude','longitude']].mean().reset_index()\n"
    "        m = rank.merge(coords, on=GROUP_KEY, how='left')\n"
    "        for _, r in m.iterrows():\n"
    "            if pd.notna(r.get('latitude')) and pd.notna(r.get('longitude')):\n"
    "                folium.CircleMarker([r['latitude'], r['longitude']],\n"
    "                                    radius=5+2*(r['score_potencial']/10),\n"
    "                                    color='crimson', fill=True,\n"
    "                                    popup=str(r[GROUP_KEY])+': '+str(round(r['score_potencial'],2))).add_to(fmap)\n"
    "    html_path = REPORTS/'mapa_potencial.html'\n"
    "    fmap.save(str(html_path))\n"
    "    print('Mapa salvo em', html_path)\n"
    "except Exception as e:\n"
    "    print('Folium indisponível/erro no mapa ->', e)\n"
))

# 7. Top recomendações
nb.cells.append(md('## 7. Top recomendações'))
nb.cells.append(code(
    "top5 = rank.head(5)\n"
    "top_path = REPORTS/'top_recomendacoes.csv'\n"
    "top5.to_csv(top_path, index=False)\n"
    "print('Top recomendações em', top_path)\n"
))

# 8. Persistência do modelo
nb.cells.append(md('## 8. Persistência do modelo'))
nb.cells.append(code(
    "import joblib\n"
    "model_path = MODELS/'territorial_rf.pkl'\n"
    "joblib.dump({'model': rf, 'features': Xnum.columns.tolist(), 'group_key': GROUP_KEY}, model_path)\n"
    "print('Modelo salvo em', model_path)\n"
))

# 9. Export de métricas
nb.cells.append(md('## 9. Métricas de validação'))
nb.cells.append(code(
    "mrows = []\n"
    "for k,(m,s) in metrics.items():\n"
    "    mrows.append({'metric': k.replace('test_',''), 'mean': -m if 'neg_' in k else m, 'std': -s if 'neg_' in k else s})\n"
    "met = pd.DataFrame(mrows)\n"
    "m_path = REPORTS/'validation_metrics.csv'\n"
    "met.to_csv(m_path, index=False)\n"
    "met\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook built at', NB_PATH)

