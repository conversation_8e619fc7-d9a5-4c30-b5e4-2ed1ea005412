from pathlib import Path
import nbformat as nbf

NB = Path('notebooks') / '05_customer_segmentation.ipynb'

CLUSTER_CELL = r'''# Export clustering assignments (anti-leak features)
import numpy as np, pandas as pd
from pathlib import Path
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.cluster import KMeans

BASE = Path('.')
if not (BASE/'data'/'processed'/'features_engineered_regional.csv').exists(): BASE = Path('..')
DATA = BASE/'data'/'processed'/'features_engineered_regional.csv' if (BASE/'data'/'processed'/'features_engineered_regional.csv').exists() else BASE/'data'/'processed'/'features_engineered.csv'
REPORTS = BASE/'reports'/'2025-08-15'
TABLES = REPORTS/'tables'; PLOTS = REPORTS/'plots'
TABLES.mkdir(parents=True, exist_ok=True); PLOTS.mkdir(parents=True, exist_ok=True)

df = pd.read_csv(DATA, low_memory=False)
# Determine target
TARGET = 'valor' if 'valor' in df.columns else None
base_targets = [t for t in [TARGET,'valor','revenue','sales','y'] if t]

# Build clean numeric features
num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]

def _is_leak(c:str)->bool:
    cl=c.lower(); pats=[]
    for t in base_targets:
        t=t.lower(); pats += [t, f'_{t}', f'{t}_', f'sq_{t}', f'{t}_boxcox', f'{t}_log', f'{t}_log1p', f'{t}_sqrt', f'log_{t}', f'boxcox_{t}']
    return any(p in cl for p in pats)
num_clean = [c for c in num_cols if not _is_leak(c)]

# Territorial categorical columns
cat_cols = []
if 'UF' in df.columns: cat_cols.append('UF')
reg_oh = [c for c in df.columns if 'REGIAO_CHILLI' in c]
# Prefer UF as categorical and pass through REGIAO one-hot ints as numeric context

# Preprocessor
transformers = []
if cat_cols:
    transformers.append(('cat', OneHotEncoder(handle_unknown='ignore'), cat_cols))
fixed_num = [c for c in num_clean if c not in base_targets]
if fixed_num:
    transformers.append(('num', StandardScaler(with_mean=False), fixed_num))
if reg_oh:
    transformers.append(('reg', 'passthrough', reg_oh))
pre = ColumnTransformer(transformers)

X = pre.fit_transform(df)
# Choose k via heuristic (elbow-like cap)
k = 5 if X.shape[0] > 2000 else 4
km = KMeans(n_clusters=k, random_state=42, n_init=10)
labels_full = km.fit_predict(X)

assign = pd.DataFrame({'index': np.arange(len(labels_full)), 'cluster': labels_full})
assign.to_csv(TABLES/'cluster_assignments.csv', index=False)
print('Saved clustering assignments ->', TABLES/'cluster_assignments.csv')
'''

if __name__ == '__main__':
    nb = nbf.read(NB, as_version=4)
    nb.cells.append(nbf.v4.new_code_cell(CLUSTER_CELL))
    nbf.write(nb, NB)
    print('Patched 05 with clustering export cell')

