{"cells": [{"cell_type": "markdown", "id": "753fce2b", "metadata": {}, "source": ["# Análise Territorial Abrangente – <PERSON>lli<PERSON><PERSON> (Vanessa)\n", "\n", "**Objetivo:** Comparar abordagens supervisionadas e não-supervisionadas para ranquear regiões por potencial de expansão.\n", "\n", "Este notebook demonstra ambas as metodologias, permitindo à equipe escolher a estratégia mais adequada conforme o contexto de negócio."]}, {"cell_type": "markdown", "id": "9e10bd46", "metadata": {}, "source": ["## 1. <PERSON><PERSON><PERSON><PERSON> Exploratória Geográfica"]}, {"cell_type": "code", "execution_count": 1, "id": "d174b8e0", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:21.188204Z", "iopub.status.busy": "2025-09-11T13:22:21.187888Z", "iopub.status.idle": "2025-09-11T13:22:24.330046Z", "shell.execute_reply": "2025-09-11T13:22:24.329503Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Carregando dados de: ..\\data\\processed\\features_engineered.csv\n"]}], "source": ["from pathlib import Path\n", "import pandas as pd, numpy as np, json\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.cluster import KMeans\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import KFold, cross_validate\n", "from sklearn.metrics import silhouette_score\n", "import warnings; warnings.filterwarnings('ignore')\n", "import joblib\n", "BASE = Path('.')\n", "if not (BASE/'data'/'processed'/'features_engineered.csv').exists(): BASE = Path('..')\n", "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n", "REPORTS = BASE/'reports'/'2025-08-15'/'territorial_analysis'\n", "MODELS = BASE/'models'\n", "for d in [REPORTS, MODELS]: d.mkdir(parents=True, exist_ok=True)\n", "print('Carregando dados de:', DATA)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "fce94ec8", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:24.332533Z", "iopub.status.busy": "2025-09-11T13:22:24.331555Z", "iopub.status.idle": "2025-09-11T13:22:25.493682Z", "shell.execute_reply": "2025-09-11T13:22:25.492700Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset shape: (35616, 182)\n", "Colunas regionais disponíveis: ['Dim_<PERSON><PERSON>.Cod_Franqueado', 'cidade_freq', 'cidade_was_missing', 'uf_was_missing', 'Dim_Cliente.Regiao_Cliente_was_missing', 'Dim_<PERSON><PERSON>.Bairro_Emp_was_missing', 'Dim_<PERSON>jas.ID_SAP_was_missing', 'Dim_Lojas.Cod_Franqueado_was_missing', 'cidade_freq_was_missing', 'Tipo_PDV_LOJA                '] ...\n", "Chave de agregação selecionada: grupo_sintetico (cardinalidade: 9)\n"]}], "source": ["# Carregamento e preparação inicial\n", "df = pd.read_csv(DATA, low_memory=False)\n", "print('Dataset shape:', df.shape)\n", "# Identificar colunas regionais disponíveis (buscar por padrões)\n", "regional_candidates = ['regiao','uf','cidade','bairro','latitude','longitude','Tipo_PDV']\n", "# Buscar por colunas que contenham padrões regionais\n", "regional_patterns = ['regiao', 'uf', 'cidade', 'tipo_pdv', 'canal', 'loja']\n", "available_regional = []\n", "for col in df.columns:\n", "    col_lower = col.lower().replace('_', '').replace('.', '')\n", "    if any(pattern in col_lower for pattern in regional_patterns):\n", "        available_regional.append(col)\n", "print('Colunas regionais disponíveis:', available_regional[:10], '...' if len(available_regional)>10 else '')\n", "# Definir chave de agregação (priorizar colunas categóricas com boa cardinalidade)\n", "GROUP_KEY = None\n", "for col in available_regional:\n", "    if df[col].dtype == 'object' or (df[col].dtype in ['int64','float64'] and df[col].nunique() < len(df)/10):\n", "        if df[col].nunique() > 5 and df[col].nunique() < 100:  # Boa cardinalidade\n", "            GROUP_KEY = col; break\n", "# Fallback: usar primeira coluna categórica disponível\n", "if not GROUP_KEY:\n", "    cat_cols = [c for c in df.columns if df[c].dtype == 'object' and df[c].nunique() > 3]\n", "    if cat_cols:\n", "        GROUP_KEY = cat_cols[0]\n", "    else:\n", "        # Último fallback: criar grupos sintéticos baseados em percentis de valor\n", "        try:\n", "            df['grupo_sintetico'] = pd.qcut(df['valor'], q=10, labels=False, duplicates='drop')\n", "            df['grupo_sintetico'] = 'Grupo_' + (df['grupo_sintetico'] + 1).astype(str)\n", "        except Exception:\n", "            # Se qcut falhar, usar binning simples\n", "            df['grupo_sintetico'] = pd.cut(df['valor'], bins=10, labels=False)\n", "            df['grupo_sintetico'] = 'Grupo_' + (df['grupo_sintetico'] + 1).astype(str)\n", "        GROUP_KEY = 'grupo_sintetico'\n", "print('Chave de agregação selecionada:', GROUP_KEY, f'(cardinalidade: {df[GROUP_KEY].nunique()})')\n"]}, {"cell_type": "code", "execution_count": 3, "id": "09b7e238", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:25.497775Z", "iopub.status.busy": "2025-09-11T13:22:25.496797Z", "iopub.status.idle": "2025-09-11T13:22:25.532082Z", "shell.execute_reply": "2025-09-11T13:22:25.532082Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dados regionais agregados: (9, 12)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grupo_sintetico</th>\n", "      <th>valor_sum</th>\n", "      <th>valor_mean</th>\n", "      <th>valor_median</th>\n", "      <th>valor_count</th>\n", "      <th>valor_std</th>\n", "      <th>qtd_sum</th>\n", "      <th>qtd_mean</th>\n", "      <th>Preco_Custo_sum</th>\n", "      <th>Preco_Custo_mean</th>\n", "      <th>Desconto_sum</th>\n", "      <th>Desconto_mean</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Grupo_1</td>\n", "      <td>-3952.370075</td>\n", "      <td>-0.908173</td>\n", "      <td>-0.901927</td>\n", "      <td>4352</td>\n", "      <td>0.013919</td>\n", "      <td>187.0</td>\n", "      <td>0.042969</td>\n", "      <td>-4124.817002</td>\n", "      <td>-0.947798</td>\n", "      <td>67521.62</td>\n", "      <td>15.515078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Grupo_2</td>\n", "      <td>-2562.250071</td>\n", "      <td>-0.873594</td>\n", "      <td>-0.870281</td>\n", "      <td>2933</td>\n", "      <td>0.010635</td>\n", "      <td>55.0</td>\n", "      <td>0.018752</td>\n", "      <td>-3069.678419</td>\n", "      <td>-1.046600</td>\n", "      <td>28179.66</td>\n", "      <td>9.607794</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Grupo_3</td>\n", "      <td>-2255.094940</td>\n", "      <td>-0.662679</td>\n", "      <td>-0.683471</td>\n", "      <td>3403</td>\n", "      <td>0.148454</td>\n", "      <td>696.0</td>\n", "      <td>0.204525</td>\n", "      <td>-1049.190629</td>\n", "      <td>-0.308313</td>\n", "      <td>41844.76</td>\n", "      <td>12.296433</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Grupo_4</td>\n", "      <td>-1099.864711</td>\n", "      <td>-0.308951</td>\n", "      <td>-0.316466</td>\n", "      <td>3560</td>\n", "      <td>0.050752</td>\n", "      <td>209.0</td>\n", "      <td>0.058708</td>\n", "      <td>-145.986569</td>\n", "      <td>-0.041007</td>\n", "      <td>106494.99</td>\n", "      <td>29.914323</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Grupo_5</td>\n", "      <td>-171.018355</td>\n", "      <td>-0.043023</td>\n", "      <td>0.000000</td>\n", "      <td>3975</td>\n", "      <td>0.054857</td>\n", "      <td>252.0</td>\n", "      <td>0.063396</td>\n", "      <td>337.282204</td>\n", "      <td>0.084851</td>\n", "      <td>99746.36</td>\n", "      <td>25.093424</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  grupo_sintetico    valor_sum  valor_mean  valor_median  valor_count  \\\n", "0         Grupo_1 -3952.370075   -0.908173     -0.901927         4352   \n", "1         Grupo_2 -2562.250071   -0.873594     -0.870281         2933   \n", "2         Grupo_3 -2255.094940   -0.662679     -0.683471         3403   \n", "3         Grupo_4 -1099.864711   -0.308951     -0.316466         3560   \n", "4         Grupo_5  -171.018355   -0.043023      0.000000         3975   \n", "\n", "   valor_std  qtd_sum  qtd_mean  Preco_Custo_sum  Preco_Custo_mean  \\\n", "0   0.013919    187.0  0.042969     -4124.817002         -0.947798   \n", "1   0.010635     55.0  0.018752     -3069.678419         -1.046600   \n", "2   0.148454    696.0  0.204525     -1049.190629         -0.308313   \n", "3   0.050752    209.0  0.058708      -145.986569         -0.041007   \n", "4   0.054857    252.0  0.063396       337.282204          0.084851   \n", "\n", "   Desconto_sum  Desconto_mean  \n", "0      67521.62      15.515078  \n", "1      28179.66       9.607794  \n", "2      41844.76      12.296433  \n", "3     106494.99      29.914323  \n", "4      99746.36      25.093424  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Agregações regionais para análise\n", "agg_dict = {\n", "    'valor': ['sum','mean','median','count','std'],\n", "}\n", "# Adicionar outras métricas se disponíveis\n", "for col in ['qtd', 'Preco_Custo', 'Descon<PERSON>']:\n", "    if col in df.columns:\n", "        agg_dict[col] = ['sum','mean']\n", "# Realizar agregação\n", "regional_data = df.groupby(GROUP_KEY).agg(agg_dict)\n", "regional_data.columns = ['_'.join([a,b]) for a,b in regional_data.columns]\n", "regional_data = regional_data.reset_index()\n", "print('Dados regionais agregados:', regional_data.shape)\n", "# Adicionar coordenadas médias se disponíveis\n", "if {'latitude','longitude'}.issubset(df.columns):\n", "    coords = df.groupby(GROUP_KEY)[['latitude','longitude']].mean().reset_index()\n", "    regional_data = regional_data.merge(coords, on=GROUP_KEY, how='left')\n", "regional_data.head()\n"]}, {"cell_type": "markdown", "id": "0553ff31", "metadata": {}, "source": ["### 1.1 Mapa Exploratório de Vendas"]}, {"cell_type": "code", "execution_count": 4, "id": "be8d0ca3", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:25.535021Z", "iopub.status.busy": "2025-09-11T13:22:25.534048Z", "iopub.status.idle": "2025-09-11T13:22:25.545526Z", "shell.execute_reply": "2025-09-11T13:22:25.545526Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Folium não disponível - pulando map<PERSON>mento\n"]}], "source": ["# Mapa de vendas por região usando folium\n", "try:\n", "    import folium\n", "    center = [-14.2350, -51.9253]  # Centro do Brasil\n", "    m_exploratory = folium.Map(location=center, zoom_start=4)\n", "    if {'latitude','longitude'}.issubset(regional_data.columns):\n", "        for _, row in regional_data.iterrows():\n", "            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):\n", "                receita = row.get('valor_sum', 0)\n", "                radius = max(5, min(20, receita/10000))  # Escala do círculo\n", "                folium.CircleMarker(\n", "                    [row['latitude'], row['longitude']],\n", "                    radius=radius,\n", "                    color='blue',\n", "                    fill=True,\n", "                    popup=f\"{row[GROUP_KEY]}: R$ {receita:,.0f}\"\n", "                ).add_to(m_exploratory)\n", "        m_exploratory.save(str(REPORTS/'mapa_exploratorio.html'))\n", "        print('Mapa exploratório salvo em:', REPORTS/'mapa_exploratorio.html')\n", "    else:\n", "        print('Coordenadas não disponíveis para mapeamento')\n", "except ImportError:\n", "    print('Folium não disponível - pulando mapeamento')\n"]}, {"cell_type": "markdown", "id": "68d88c94", "metadata": {}, "source": ["### 1.2 Preparação de Features para Modelagem"]}, {"cell_type": "code", "execution_count": 5, "id": "ec600d2c", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:25.549085Z", "iopub.status.busy": "2025-09-11T13:22:25.549085Z", "iopub.status.idle": "2025-09-11T13:22:25.563906Z", "shell.execute_reply": "2025-09-11T13:22:25.562924Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Features para modelagem: ['valor_mean', 'valor_median', 'valor_count', 'valor_std', 'qtd_sum', 'qtd_mean', 'Preco_Custo_sum', 'Preco_Custo_mean', 'Desconto_sum', 'Desconto_mean']\n", "Target (receita): min=-3952, max=2973, mean=-327\n"]}], "source": ["# Selecionar features numéricas para modelagem\n", "feature_cols = [c for c in regional_data.columns if c != GROUP_KEY and pd.api.types.is_numeric_dtype(regional_data[c])]\n", "# Remover coordenadas das features (usar apenas para visualização)\n", "feature_cols = [c for c in feature_cols if c not in ['latitude','longitude']]\n", "# Preparar matriz de features\n", "X_raw = regional_data[feature_cols].fillna(regional_data[feature_cols].median())\n", "# Definir target para abordagem supervisionada\n", "if 'valor_sum' in regional_data.columns:\n", "    y_raw = regional_data['valor_sum'].values\n", "    X_features = X_raw.drop(columns=['valor_sum'], errors='ignore')\n", "else:\n", "    # Fallback: usar primeira coluna numérica como proxy\n", "    y_raw = X_raw.iloc[:,0].values\n", "    X_features = X_raw.iloc[:,1:]\n", "print('Features para modelagem:', X_features.columns.tolist())\n", "print('Target (receita):', f'min={y_raw.min():.0f}, max={y_raw.max():.0f}, mean={y_raw.mean():.0f}')\n"]}, {"cell_type": "markdown", "id": "d100b7e3", "metadata": {}, "source": ["## 2. Abordagem Supervisionada - Modelo Preditivo"]}, {"cell_type": "code", "execution_count": 6, "id": "9ac0a95f", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:25.566926Z", "iopub.status.busy": "2025-09-11T13:22:25.565671Z", "iopub.status.idle": "2025-09-11T13:22:29.053066Z", "shell.execute_reply": "2025-09-11T13:22:29.053066Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Métricas Supervisionadas (CV 5-fold):\n", "  rmse: -1669.3370 ± 626.1552\n", "  mae: -1505.8784 ± 594.2421\n", "  r2: nan ± nan\n"]}], "source": ["# Normalizar features para modelagem\n", "scaler_sup = StandardScaler()\n", "X_scaled = scaler_sup.fit_transform(X_features)\n", "# Configurar modelo Random Forest\n", "rf_model = RandomForestRegressor(\n", "    n_estimators=400,\n", "    max_depth=10,\n", "    min_samples_split=5,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "# Validação cruzada 5-fold\n", "cv = KFold(n_splits=5, shuffle=True, random_state=42)\n", "scoring = {'rmse':'neg_root_mean_squared_error','mae':'neg_mean_absolute_error','r2':'r2'}\n", "cv_results = cross_validate(rf_model, X_scaled, y_raw, cv=cv, scoring=scoring, n_jobs=1)\n", "# Comp<PERSON>r mé<PERSON>\n", "metrics_sup = {}\n", "for metric, scores in cv_results.items():\n", "    if metric.startswith('test_'):\n", "        name = metric.replace('test_', '')\n", "        values = -scores if 'neg_' in metric else scores\n", "        metrics_sup[name] = {'mean': float(np.mean(values)), 'std': float(np.std(values))}\n", "print('Métricas Supervisionadas (CV 5-fold):')\n", "for name, stats in metrics_sup.items():\n", "    print(f'  {name}: {stats[\"mean\"]:.4f} ± {stats[\"std\"]:.4f}')\n"]}, {"cell_type": "code", "execution_count": 7, "id": "1c4b9e86", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:29.057012Z", "iopub.status.busy": "2025-09-11T13:22:29.056004Z", "iopub.status.idle": "2025-09-11T13:22:29.689564Z", "shell.execute_reply": "2025-09-11T13:22:29.688581Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Top 10 Regiões - Abordagem Supervisionada:\n", "   rank_supervisionado grupo_sintetico  score_supervisionado\n", "0                    1         Grupo_9             10.000000\n", "1                    2         Grupo_8              9.765741\n", "2                    3         Grupo_6              9.523044\n", "3                    4         Grupo_7              9.499156\n", "4                    5         Grupo_5              6.908325\n", "5                    6         Grupo_4              3.338024\n", "6                    7         Grupo_3              0.376502\n", "7                    8         Grupo_2              0.077041\n", "8                    9         Grupo_1              0.000000\n"]}], "source": ["# Treinar modelo final e gerar scores 0-10\n", "rf_model.fit(X_scaled, y_raw)\n", "y_pred = rf_model.predict(X_scaled)\n", "# Normalizar para score 0-10\n", "score_scaler = MinMaxScaler(feature_range=(0, 10))\n", "scores_supervised = score_scaler.fit_transform(y_pred.reshape(-1, 1)).ravel()\n", "# Criar ranking supervisionado\n", "ranking_sup = regional_data[[GROUP_KEY]].copy()\n", "ranking_sup['receita_real'] = y_raw\n", "ranking_sup['receita_pred'] = y_pred\n", "ranking_sup['score_supervisionado'] = scores_supervised\n", "ranking_sup = ranking_sup.sort_values('score_supervisionado', ascending=False).reset_index(drop=True)\n", "ranking_sup['rank_supervisionado'] = range(1, len(ranking_sup) + 1)\n", "# Salvar ranking\n", "ranking_sup.to_csv(REPORTS/'ranking_supervisionado.csv', index=False)\n", "print('Top 10 Regiões - Abordagem Supervisionada:')\n", "print(ranking_sup[['rank_supervisionado', GROUP_KEY, 'score_supervisionado']].head(10))\n"]}, {"cell_type": "markdown", "id": "273c72c5", "metadata": {}, "source": ["## 3. Abordagem Não-Supervisionada - Segmentação Territorial"]}, {"cell_type": "code", "execution_count": 8, "id": "f032db73", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:29.692060Z", "iopub.status.busy": "2025-09-11T13:22:29.692060Z", "iopub.status.idle": "2025-09-11T13:22:30.387657Z", "shell.execute_reply": "2025-09-11T13:22:30.387657Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["K=3: Inertia=38.43, <PERSON><PERSON><PERSON><PERSON>=0.248\n", "K=4: Inertia=26.45, <PERSON><PERSON><PERSON><PERSON>=0.264\n", "K=5: Inertia=10.74, <PERSON><PERSON><PERSON><PERSON>=0.331\n", "K=6: Inertia=4.90, <PERSON><PERSON><PERSON><PERSON>=0.263\n"]}, {"name": "stdout", "output_type": "stream", "text": ["K=7: Inertia=1.95, <PERSON><PERSON><PERSON><PERSON>=0.240\n", "K=8: Inertia=0.48, <PERSON><PERSON><PERSON><PERSON>=0.121\n", "\n", "Mel<PERSON> K selecionado: 5 (<PERSON><PERSON><PERSON><PERSON>: 0.331)\n"]}], "source": ["# Normalizar features para clustering\n", "scaler_unsup = StandardScaler()\n", "X_scaled_unsup = scaler_unsup.fit_transform(X_features)\n", "# Método do cotovelo + Sil<PERSON>ette para seleção de K\n", "k_range = range(3, 9)\n", "inertias = []\n", "silhouette_scores = []\n", "for k in k_range:\n", "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "    labels = kmeans.fit_predict(X_scaled_unsup)\n", "    inertias.append(kmeans.inertia_)\n", "    sil_score = silhouette_score(X_scaled_unsup, labels)\n", "    silhouette_scores.append(sil_score)\n", "    print(f'K={k}: Inertia={kmeans.inertia_:.2f}, Silhouette={sil_score:.3f}')\n", "# Selecionar melhor K por silhouette\n", "best_k = k_range[np.argmax(silhouette_scores)]\n", "best_silhouette = max(silhouette_scores)\n", "print(f'\\nMelhor K selecionado: {best_k} (<PERSON><PERSON>houette: {best_silhouette:.3f})')\n"]}, {"cell_type": "code", "execution_count": 9, "id": "a8e879a9", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:30.392110Z", "iopub.status.busy": "2025-09-11T13:22:30.392110Z", "iopub.status.idle": "2025-09-11T13:22:30.465972Z", "shell.execute_reply": "2025-09-11T13:22:30.464986Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Estatísticas por Cluster:\n", "         valor_sum_count  valor_sum_mean  valor_sum_sum\n", "cluster                                                \n", "0                      2        -3257.31       -6514.62\n", "1                      4          119.29         477.17\n", "2                      1         2372.98        2372.98\n", "3                      1         2972.50        2972.50\n", "4                      1        -2255.09       -2255.09\n", "\n", "Top 10 Regiões por Cluster (ordenado por performance do cluster):\n", "  grupo_sintetico  cluster  cluster_rank\n", "8         Grupo_9        3             1\n", "6         Grupo_7        2             2\n", "7         Grupo_8        1             3\n", "5         Grupo_6        1             3\n", "4         Grupo_5        1             3\n", "3         Grupo_4        1             3\n", "2         Grupo_3        4             4\n", "1         Grupo_2        0             5\n", "0         Grupo_1        0             5\n"]}], "source": ["# Treinar modelo final K-Means\n", "kmeans_final = KMeans(n_clusters=best_k, random_state=42, n_init=10)\n", "cluster_labels = kmeans_final.fit_predict(X_scaled_unsup)\n", "# Analisar clusters\n", "cluster_analysis = regional_data.copy()\n", "cluster_analysis['cluster'] = cluster_labels\n", "# Estatísticas por cluster\n", "if 'valor_sum' in cluster_analysis.columns:\n", "    agg_dict = {'valor_sum': ['count', 'mean', 'sum']}\n", "else:\n", "    agg_dict = {GROUP_KEY: 'count'}\n", "cluster_stats = cluster_analysis.groupby('cluster').agg(agg_dict).round(2)\n", "cluster_stats.columns = ['_'.join(col).strip() for col in cluster_stats.columns]\n", "print('Estatísticas por Cluster:')\n", "print(cluster_stats)\n", "# Criar ranking por cluster (baseado na receita média do cluster)\n", "if 'valor_sum' in cluster_analysis.columns:\n", "    cluster_performance = cluster_analysis.groupby('cluster')['valor_sum'].mean().sort_values(ascending=False)\n", "    cluster_ranking = {cluster: rank+1 for rank, cluster in enumerate(cluster_performance.index)}\n", "else:\n", "    cluster_ranking = {i: i+1 for i in range(best_k)}\n", "cluster_analysis['cluster_rank'] = cluster_analysis['cluster'].map(cluster_ranking)\n", "cluster_analysis = cluster_analysis.sort_values(['cluster_rank', 'valor_sum'], ascending=[True, False])\n", "# Salvar resultados\n", "cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']].to_csv(REPORTS/'clusters_nao_supervisionado.csv', index=False)\n", "print('\\nTop 10 Regiões por Cluster (ordenado por performance do cluster):')\n", "print(cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']].head(10))\n"]}, {"cell_type": "markdown", "id": "4acedee6", "metadata": {}, "source": ["## 4. Comparação Estratégica Entre Abordagens"]}, {"cell_type": "code", "execution_count": 10, "id": "588505fc", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:30.468074Z", "iopub.status.busy": "2025-09-11T13:22:30.468074Z", "iopub.status.idle": "2025-09-11T13:22:30.483289Z", "shell.execute_reply": "2025-09-11T13:22:30.483289Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Taxa de concordância entre métodos: 88.89%\n", "\n", "Regiões priorizadas por AMBOS os métodos:\n", "  grupo_sintetico  rank_supervisionado  cluster_rank  score_supervisionado\n", "0         Grupo_9                    1             1             10.000000\n", "1         Grupo_8                    2             3              9.765741\n", "2         Grupo_6                    3             3              9.523044\n", "3         Grupo_7                    4             2              9.499156\n", "4         Grupo_5                    5             3              6.908325\n", "5         Grupo_4                    6             3              3.338024\n"]}], "source": ["# Merge dos resultados para comparação\n", "comparison = ranking_sup[[GROUP_KEY, 'rank_supervisionado', 'score_supervisionado']].merge(\n", "    cluster_analysis[[GROUP_KEY, 'cluster', 'cluster_rank']], on=GROUP_KEY, how='inner'\n", ")\n", "# Análise de concordância\n", "comparison['rank_diff'] = abs(comparison['rank_supervisionado'] - comparison['cluster_rank'])\n", "comparison['concordancia'] = comparison['rank_diff'] <= 3  # Concordância se diferença <= 3 posições\n", "concordancia_rate = comparison['concordancia'].mean()\n", "print(f'Taxa de concordância entre métodos: {concordancia_rate:.2%}')\n", "# Top regiões por ambos os métodos\n", "top_both = comparison[\n", "    (comparison['rank_supervisionado'] <= 10) & (comparison['cluster_rank'] <= 3)\n", "].sort_values('rank_supervisionado')\n", "print('\\nRegiões priorizadas por AMBOS os métodos:')\n", "print(top_both[[GROUP_KEY, 'rank_supervisionado', 'cluster_rank', 'score_supervisionado']])\n", "# Salvar comparação\n", "comparison.to_csv(REPORTS/'comparacao_metodos.csv', index=False)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "b8cb561c", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:30.486225Z", "iopub.status.busy": "2025-09-11T13:22:30.485250Z", "iopub.status.idle": "2025-09-11T13:22:30.493054Z", "shell.execute_reply": "2025-09-11T13:22:30.493054Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ANÁLISE COMPARATIVA ===\n", "\n", "ABORDAGEM SUPERVISIONADA:\n", "- Foco: Predição de receita baseada em padrões históricos\n", "- Vantagem: Quantifica potencial financeiro diretamente\n", "- Limitação: Dependente da qualidade dos dados históricos\n", "- Uso recomendado: Expansão em mercados similares aos existentes\n", "\n", "ABORDAGEM NÃO-SUPERVISIONADA:\n", "- Foco: Identificação de padrões e segmentos territoriais\n", "- Vantagem: Descobre grupos não óbvios, útil para estratégias diferenciadas\n", "- Limitação: Não quantifica diretamente o potencial financeiro\n", "- Uso recomendado: Exploração de novos mercados, estratégias por segmento\n", "\n", "CONCORDÂNCIA:\n", "- 88.9% das regiões têm ranking similar entre métodos\n", "- 6 regiões são priorizadas por ambos os métodos\n"]}], "source": ["# An<PERSON>lise das diferenças metodológicas\n", "print('=== ANÁLISE COMPARATIVA ===')\n", "print('\\nABORDAGEM SUPERVISIONADA:')\n", "print('- Foco: Predição de receita baseada em padrões históricos')\n", "print('- Vantagem: Quantifica potencial financeiro diretamente')\n", "print('- Limitação: Dependente da qualidade dos dados históricos')\n", "print('- Uso recomendado: Expansão em mercados similares aos existentes')\n", "print('\\nABORDAGEM NÃO-SUPERVISIONADA:')\n", "print('- Foco: Identificação de padrões e segmentos territoriais')\n", "print('- Vantagem: Descobre grupos não óbvios, útil para estratégias diferenciadas')\n", "print('- Limitação: Não quantifica diretamente o potencial financeiro')\n", "print('- <PERSON><PERSON> recomendado: Exploração de novos mercados, estratégias por segmento')\n", "print('\\nCONCORDÂNCIA:')\n", "print(f'- {concordancia_rate:.1%} das regiões têm ranking similar entre métodos')\n", "print(f'- {len(top_both)} regiões são priorizadas por ambos os métodos')\n"]}, {"cell_type": "markdown", "id": "9b98cb94", "metadata": {}, "source": ["## 5. Dashboard Interativo e Visualizações"]}, {"cell_type": "code", "execution_count": 12, "id": "525afda8", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:30.495989Z", "iopub.status.busy": "2025-09-11T13:22:30.495989Z", "iopub.status.idle": "2025-09-11T13:22:30.507177Z", "shell.execute_reply": "2025-09-11T13:22:30.507177Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Folium não disponível - pulando mapeamento interativo\n"]}], "source": ["# Mapa interativo completo\n", "try:\n", "    import folium\n", "    from folium import plugins\n", "    # Mapa base\n", "    center = [-14.2350, -51.9253]\n", "    m_complete = folium.Map(location=center, zoom_start=4)\n", "    # Cores para clusters\n", "    cluster_colors = ['red', 'blue', 'green', 'purple', 'orange', 'darkred', 'lightred', 'beige']\n", "    if {'latitude','longitude'}.issubset(regional_data.columns):\n", "        # Merge dados completos\n", "        map_data = regional_data.merge(comparison, on=GROUP_KEY, how='left')\n", "        for _, row in map_data.iterrows():\n", "            if pd.notna(row.get('latitude')) and pd.notna(row.get('longitude')):\n", "                # <PERSON><PERSON><PERSON> baseado no score supervisionado\n", "                radius = max(5, min(15, row.get('score_supervisionado', 5)))\n", "                # Cor baseada no cluster\n", "                cluster_idx = int(row.get('cluster', 0)) % len(cluster_colors)\n", "                color = cluster_colors[cluster_idx]\n", "                # Popup informativo\n", "                popup_text = f\"\"\"\n", "                <b>{row[GROUP_KEY]}</b><br>\n", "                Score Supervisionado: {row.get('score_supervisionado', 'N/A'):.1f}<br>\n", "                Cluster: {row.get('cluster', 'N/A')}<br>\n", "                Rank Supervisionado: {row.get('rank_supervisionado', 'N/A')}<br>\n", "                Rank Cluster: {row.get('cluster_rank', 'N/A')}\n", "                \"\"\"\n", "                folium.CircleMarker(\n", "                    [row['latitude'], row['longitude']],\n", "                    radius=radius,\n", "                    color=color,\n", "                    fill=True,\n", "                    popup=folium.Popup(popup_text, max_width=300)\n", "                ).add_to(m_complete)\n", "        # Adicionar legenda\n", "        legend_html = '<div style=\"position: fixed; top: 10px; right: 10px; z-index:1000; background-color:white; padding:10px; border:2px solid grey;\">'\n", "        legend_html += '<h4>Legenda</h4>'\n", "        legend_html += '<p><b><PERSON><PERSON><PERSON>:</b> Score Supervisionado</p>'\n", "        legend_html += '<p><b>Cor:</b> Cluster Não-Supervisionado</p>'\n", "        legend_html += '</div>'\n", "        m_complete.get_root().html.add_child(folium.Element(legend_html))\n", "        m_complete.save(str(REPORTS/'mapa_interativo_completo.html'))\n", "        print('Mapa interativo completo salvo em:', REPORTS/'mapa_interativo_completo.html')\n", "    else:\n", "        print('Coordenadas não disponíveis para mapeamento completo')\n", "except ImportError:\n", "    print('Folium não disponível - pulando mapeamento interativo')\n"]}, {"cell_type": "markdown", "id": "782a5b3a", "metadata": {}, "source": ["## 6. Recomendaç<PERSON><PERSON> Executivas"]}, {"cell_type": "code", "execution_count": 13, "id": "e483f3d4", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:30.510841Z", "iopub.status.busy": "2025-09-11T13:22:30.509315Z", "iopub.status.idle": "2025-09-11T13:22:30.520930Z", "shell.execute_reply": "2025-09-11T13:22:30.520402Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== SÍNTESE EXECUTIVA ===\n", "\n", "Total de regiões analisadas: 9\n", "Clusters identificados: 5\n", "Taxa de concordância entre métodos: 88.9%\n", "\n", "=== TOP 5 REGIÕES INTEGRADAS ===\n", "1. Grupo_9\n", "   Score Supervisionado: 10.0 (Rank: 1)\n", "   Cluster: 3 (Rank: 1)\n", "   Score Integrado: 1.000\n", "\n", "2. Grupo_8\n", "   Score Supervisionado: 9.8 (Rank: 2)\n", "   Cluster: 1 (Rank: 3)\n", "   Score Integrado: 0.810\n", "\n", "3. Grupo_6\n", "   Score Supervisionado: 9.5 (Rank: 3)\n", "   Cluster: 1 (Rank: 3)\n", "   Score Integrado: 0.740\n", "\n", "4. Grupo_7\n", "   Score Supervisionado: 9.5 (Rank: 4)\n", "   Cluster: 2 (Rank: 2)\n", "   Score Integrado: 0.730\n", "\n", "5. Grupo_5\n", "   Score Supervisionado: 6.9 (Rank: 5)\n", "   Cluster: 1 (Rank: 3)\n", "   Score Integrado: 0.600\n", "\n"]}], "source": ["# Síntese dos resultados\n", "print('=== SÍNTESE EXECUTIVA ===')\n", "print(f'\\nTotal de regiões analisadas: {len(regional_data)}')\n", "print(f'Clusters identificados: {best_k}')\n", "print(f'Taxa de concordância entre métodos: {concordancia_rate:.1%}')\n", "# Top 5 integrado (priorizando concordância)\n", "top5_integrated = comparison.copy()\n", "# Score integrado: média ponderada (70% supervisionado, 30% cluster)\n", "top5_integrated['score_integrado'] = (\n", "    0.7 * (11 - top5_integrated['rank_supervisionado']) / 10 +  # Normalizar rank\n", "    0.3 * (best_k + 1 - top5_integrated['cluster_rank']) / best_k\n", ")\n", "top5_integrated = top5_integrated.sort_values('score_integrado', ascending=False)\n", "print('\\n=== TOP 5 REGIÕES INTEGRADAS ===')\n", "for i, (_, row) in enumerate(top5_integrated.head(5).iterrows(), 1):\n", "    print(f'{i}. {row[GROUP_KEY]}')\n", "    print(f'   Score Supervisionado: {row[\"score_supervisionado\"]:.1f} (Rank: {row[\"rank_supervisionado\"]})')\n", "    print(f'   Cluster: {row[\"cluster\"]} (Rank: {row[\"cluster_rank\"]})')\n", "    print(f'   Score Integrado: {row[\"score_integrado\"]:.3f}')\n", "    print()\n"]}, {"cell_type": "code", "execution_count": 14, "id": "d601b18c", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:30.523428Z", "iopub.status.busy": "2025-09-11T13:22:30.523428Z", "iopub.status.idle": "2025-09-11T13:22:30.536244Z", "shell.execute_reply": "2025-09-11T13:22:30.536244Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Recomendações executivas salvas em: ..\\reports\\2025-08-15\\territorial_analysis\\recomendacoes_executivas.csv\n"]}], "source": ["# Recomendações específicas por método\n", "recommendations = []\n", "# Top 5 supervisionado\n", "for i, (_, row) in enumerate(ranking_sup.head(5).iterrows(), 1):\n", "    recommendations.append({\n", "        'rank': i,\n", "        'regiao': row[GROUP_KEY],\n", "        'metodo': 'Supervisionado',\n", "        'score': row['score_supervisionado'],\n", "        'justificativa': f'Alto potencial de receita predito (R$ {row[\"receita_pred\"]:,.0f})',\n", "        'acao_recomendada': 'Expansão prioritária - ROI esperado alto'\n", "    })\n", "# Top clusters\n", "cluster_recs = cluster_analysis.groupby('cluster').first().sort_values('cluster_rank')\n", "for i, (cluster, row) in enumerate(cluster_recs.head(3).iterrows(), 1):\n", "    recommendations.append({\n", "        'rank': i,\n", "        'regiao': f'Cluster {cluster} (ex: {row[GROUP_KEY]})',\n", "        'metodo': 'Não-Supervisionado',\n", "        'score': f'Cluster {cluster}',\n", "        'justificativa': f'Segmento territorial de alto potencial',\n", "        'acao_recomendada': f'Estratégia diferenciada para cluster {cluster}'\n", "    })\n", "# Salvar recomendações\n", "rec_df = pd.DataFrame(recommendations)\n", "rec_df.to_csv(REPORTS/'recomendacoes_executivas.csv', index=False)\n", "print('Recomendações executivas salvas em:', REPORTS/'recomendacoes_executivas.csv')\n"]}, {"cell_type": "markdown", "id": "fed738be", "metadata": {}, "source": ["### 6.1 Persistência dos Modelos"]}, {"cell_type": "code", "execution_count": 15, "id": "a71f0512", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:30.539180Z", "iopub.status.busy": "2025-09-11T13:22:30.539180Z", "iopub.status.idle": "2025-09-11T13:22:30.642441Z", "shell.execute_reply": "2025-09-11T13:22:30.641458Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Modelos salvos em:\n", "- Supervisionado: ..\\models\\territorial_supervised.pkl\n", "- Não-supervisionado: ..\\models\\territorial_unsupervised.pkl\n"]}], "source": ["# Salvar modelos treinados\n", "supervised_model = {\n", "    'model': rf_model,\n", "    'scaler': scaler_sup,\n", "    'score_scaler': score_scaler,\n", "    'features': X_features.columns.tolist(),\n", "    'metrics': metrics_sup,\n", "    'group_key': GROUP_KEY\n", "}\n", "unsupervised_model = {\n", "    'model': kmeans_final,\n", "    'scaler': scaler_unsup,\n", "    'features': X_features.columns.tolist(),\n", "    'best_k': best_k,\n", "    'silhouette_score': best_silhouette,\n", "    'group_key': GROUP_KEY\n", "}\n", "joblib.dump(supervised_model, MODELS/'territorial_supervised.pkl')\n", "joblib.dump(unsupervised_model, MODELS/'territorial_unsupervised.pkl')\n", "print('Modelos salvos em:')\n", "print('- Supervisionado:', MODELS/'territorial_supervised.pkl')\n", "print('- <PERSON><PERSON>-supervisionado:', MODELS/'territorial_unsupervised.pkl')\n"]}, {"cell_type": "markdown", "id": "9fce2fb5", "metadata": {}, "source": ["## 7. Resumo Final e Próximos Passos"]}, {"cell_type": "code", "execution_count": 16, "id": "bfbc5a3d", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T13:22:30.645407Z", "iopub.status.busy": "2025-09-11T13:22:30.645407Z", "iopub.status.idle": "2025-09-11T13:22:30.651517Z", "shell.execute_reply": "2025-09-11T13:22:30.651517Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== RESUMO FINAL ===\n", "\n", "✅ ENTREGÁVEIS GERADOS:\n", "- Ranking supervisionado: ..\\reports\\2025-08-15\\territorial_analysis\\ranking_supervisionado.csv\n", "- Clusters não-supervisionados: ..\\reports\\2025-08-15\\territorial_analysis\\clusters_nao_supervisionado.csv\n", "- Comparação de métodos: ..\\reports\\2025-08-15\\territorial_analysis\\comparacao_metodos.csv\n", "- Mapa interativo: ..\\reports\\2025-08-15\\territorial_analysis\\mapa_interativo_completo.html\n", "- Recomendações executivas: ..\\reports\\2025-08-15\\territorial_analysis\\recomendacoes_executivas.csv\n", "- Modelos treinados em models/\n", "\n", "🎯 PRÓXIMOS PASSOS:\n", "1. Validar recomendações com equipe comercial\n", "2. <PERSON><PERSON> dados externos (demografia, concorrência) para enriquecer análise\n", "3. Implementar monitoramento de performance das regiões priorizadas\n", "4. Desenvolver dashboard executivo para acompanhamento contínuo\n", "\n", "📊 CRITÉRIOS DE ESCOLHA DO MÉTODO:\n", "- Use SUPERVISIONADO quando: foco em ROI, mercados similares aos existentes\n", "- Use NÃO-SUPERVISIONADO quando: exploração de novos mercados, estratégias diferenciadas\n", "- Use AMBOS quando: decisões críticas que requerem múltiplas perspectivas\n"]}], "source": ["print('=== RESUMO FINAL ===')\n", "print('\\n✅ ENTREGÁVEIS GERADOS:')\n", "print('- Ranking supervisionado:', REPORTS/'ranking_supervisionado.csv')\n", "print('- Clusters não-supervisionados:', REPORTS/'clusters_nao_supervisionado.csv')\n", "print('- Comparação de métodos:', REPORTS/'comparacao_metodos.csv')\n", "print('- Mapa interativo:', REPORTS/'mapa_interativo_completo.html')\n", "print('- Recomendações executivas:', REPORTS/'recomendacoes_executivas.csv')\n", "print('- Modelos treinados em models/')\n", "print('\\n🎯 PRÓXIMOS PASSOS:')\n", "print('1. Validar recomendações com equipe comercial')\n", "print('2. Coletar dados externos (demografia, concorrência) para enriquecer análise')\n", "print('3. Implementar monitoramento de performance das regiões priorizadas')\n", "print('4. Desenvolver dashboard executivo para acompanhamento contínuo')\n", "print('\\n📊 CRITÉRIOS DE ESCOLHA DO MÉTODO:')\n", "print('- Use SUPERVISIONADO quando: foco em ROI, mercados similares aos existentes')\n", "print('- Use NÃO-SUPERVISIONADO quando: exploração de novos mercados, estratégias diferenciadas')\n", "print('- Use AMBOS quando: decisões críticas que requerem múltiplas perspectivas')\n"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}