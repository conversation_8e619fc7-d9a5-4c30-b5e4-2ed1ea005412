import nbformat as nbf
from pathlib import Path

md = lambda t: nbf.v4.new_markdown_cell(t)
code = lambda t: nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / '03_prescription_leads.ipynb'

nb = nbf.v4.new_notebook()

nb.cells.append(md(
    "# Prescription Leads – ChilliAnalyzer MVP (Carlos)\n\n"
    "Objetivo: identificar clientes com alta propensão de compra de lentes de grau, com lead scoring 0–100."
))

# 1. Setup
nb.cells.append(md('## 1. Setup'))
nb.cells.append(code(
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np\n"
    "import matplotlib.pyplot as plt, seaborn as sns\n"
    "from sklearn.model_selection import StratifiedKFold, cross_validate, train_test_split\n"
    "from sklearn.metrics import roc_auc_score, average_precision_score, f1_score, roc_curve, precision_recall_curve\n"
    "from sklearn.preprocessing import StandardScaler\n"
    "from sklearn.compose import ColumnTransformer\n"
    "from sklearn.pipeline import Pipeline\n"
    "from sklearn.metrics import classification_report\n"
    "import warnings; warnings.filterwarnings('ignore')\n"
    "BASE = Path('.')\n"
    "if not (BASE/'data'/'processed'/'features_engineered.csv').exists(): BASE = Path('..')\n"
    "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n"
    "REPORTS = BASE/'reports'/'2025-08-15'/'prescription_leads'\n"
    "MODELS = BASE/'models'\n"
    "for d in [REPORTS, MODELS]: d.mkdir(parents=True, exist_ok=True)\n"
))

# 2. Load & target definition
nb.cells.append(md('## 2. Carregamento e definição de target'))
nb.cells.append(code(
    "parse_dates = ['data'] if 'data' in pd.read_csv(DATA, nrows=0).columns else None\n"
    "df = pd.read_csv(DATA, parse_dates=parse_dates, low_memory=False)\n"
    "# Heurísticas para identificar compras de grau (quando metadados existem)\n"
    "text_cols = [c for c in df.columns if df[c].dtype=='object']\n"
    "has_grau = None\n"
    "for c in text_cols:\n"
    "    if df[c].str.contains('grau', case=False, na=False).any():\n"
    "        has_grau = c; break\n"
    "if has_grau is not None:\n"
    "    target = df[has_grau].str.contains('grau', case=False, na=False).astype(int)\n"
    "else:\n"
    "    # Fallback: clientes no topo do valor em janelas de 12 meses como proxy\n"
    "    # (ideal: usar categoria grau explícita)\n"
    "    cutoff = df['data'].max() - pd.Timedelta(days=365) if 'data' in df.columns else None\n"
    "    if cutoff is not None:\n"
    "        recent = df[df['data']>=cutoff]\n"
    "    else:\n"
    "        recent = df.copy()\n"
    "    thr = recent['valor'].quantile(0.7) if 'valor' in recent.columns else None\n"
    "    target = (recent['valor']>thr).reindex(df.index, fill_value=False).astype(int) if thr is not None else (df.index%2==0).astype(int)\n"
    "y = target\n"
    "# Features numéricas (excluir IDs)\n"
    "features = [c for c in df.columns if df[c].dtype!='object']\n"
    "features = [c for c in features if not (str(c).lower().startswith('id_') or str(c).lower().endswith('_id') or str(c).lower() in ['id','id_cliente','id_loja','id_produto','id_vendedor'])]\n"
    "X = df[features].fillna(df[features].median())\n"
    "print('X:', X.shape, 'target positive rate:', y.mean())\n"
))

# 3. Model definition (XGBoost or fallback)
nb.cells.append(md('## 3. Modelo e validação'))
nb.cells.append(code(
    "seed=42\n"
    "try:\n"
    "    from xgboost import XGBClassifier\n"
    "    model = XGBClassifier(random_state=seed, n_estimators=200, max_depth=6, learning_rate=0.1, subsample=0.8, colsample_bytree=0.8, n_jobs=1, eval_metric='logloss')\n"
    "    model_name = 'xgboost'\n"
    "except Exception:\n"
    "    from sklearn.ensemble import GradientBoostingClassifier\n"
    "    model = GradientBoostingClassifier(random_state=seed)\n"
    "    model_name = 'sklearn_gb'\n"
    "cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=seed)\n"
    "from sklearn.model_selection import cross_validate\n"
    "scoring = ['roc_auc','average_precision','f1']\n"
    "cvres = cross_validate(model, X, y, cv=cv, scoring=scoring, return_train_score=False, n_jobs=1)\n"
    "summary = {k: float(np.mean(v)) for k,v in cvres.items() if k.startswith('test_')}\n"
    "pd.DataFrame([summary]).to_csv(REPORTS/'cv_metrics.csv', index=False)\n"
    "summary\n"
))

# 4. Fit final + lead scoring
nb.cells.append(md('## 4. Ajuste final e lead scoring'))
nb.cells.append(code(
    "model.fit(X, y)\n"
    "proba = model.predict_proba(X)[:,1] if hasattr(model, 'predict_proba') else (model.decision_function(X)-model.decision_function(X).min())\n"
    "score = (100*(proba - proba.min())/(proba.max()-proba.min()+1e-9)).astype(float)\n"
    "out = df.copy()\n"
    "out['lead_score'] = score\n"
    "# Selecionar top 300 leads ativos (quando possível, filtrar clientes ativos)\n"
    "topN = 300\n"
    "leads = out.sort_values('lead_score', ascending=False).head(topN)\n"
    "leads[['lead_score']].to_csv(REPORTS/'qualified_leads.csv', index=False)\n"
    "print('Leads salvos em', REPORTS/'qualified_leads.csv')\n"
))

# 5. Persistência
nb.cells.append(md('## 5. Persistência do modelo'))
nb.cells.append(code(
    "import joblib\n"
    "joblib.dump({'model': model, 'features': X.columns.tolist()}, BASE/'models'/'prescription_model.pkl')\n"
    "print('Modelo salvo em', BASE/'models'/'prescription_model.pkl')\n"
))

# 6. Curvas ROC/PR (PNG)
nb.cells.append(md('## 6. Curvas ROC e PR'))
nb.cells.append(code(
    "from sklearn.metrics import roc_curve, precision_recall_curve, auc\n"
    "fpr, tpr, _ = roc_curve(y, proba)\n"
    "prec, rec, _ = precision_recall_curve(y, proba)\n"
    "plt.figure(figsize=(5,4)); plt.plot(fpr,tpr); plt.title('ROC'); plt.xlabel('FPR'); plt.ylabel('TPR'); plt.tight_layout(); plt.savefig(REPORTS/'roc.png', dpi=120)\n"
    "plt.figure(figsize=(5,4)); plt.plot(rec,prec); plt.title('PR'); plt.xlabel('Recall'); plt.ylabel('Precision'); plt.tight_layout(); plt.savefig(REPORTS/'pr.png', dpi=120)\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook built at', NB_PATH)

