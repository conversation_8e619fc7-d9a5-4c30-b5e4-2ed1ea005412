{"cells": [{"cell_type": "markdown", "id": "3e127706", "metadata": {}, "source": ["# 06 - Business Insights & Reporting\\n\\nThis notebook compiles executive summaries, KPIs, and final visualizations.\\n"]}, {"cell_type": "code", "execution_count": 1, "id": "c4d3a237", "metadata": {"execution": {"iopub.execute_input": "2025-09-11T21:04:45.068886Z", "iopub.status.busy": "2025-09-11T21:04:45.067879Z", "iopub.status.idle": "2025-09-11T21:04:45.075941Z", "shell.execute_reply": "2025-09-11T21:04:45.075941Z"}}, "outputs": [], "source": ["# Setup paths\\nfrom pathlib import Path\\nBASE = Path('.')\\nREPORTS = BASE / 'reports' / '2025-08-15'\\n(REPORTS/ 'tables').mkdir(parents=True, exist_ok=True)\\n(REPORTS/ 'plots').mkdir(parents=True, exist_ok=True)\\n(REPORTS/ 'models').mkdir(parents=True, exist_ok=True)\\nprint('Reports dir:', REPORTS)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}