{"cells": [{"cell_type": "markdown", "id": "06664cd9", "metadata": {}, "source": ["\n", "# Sprint 3 — Data Preparation & Modeling (Hybrid)\n", "\n", "Objetivo: demonstrar o processo técnico (código) com foco em:\n", "- Engenharia de atributos com anti-leakage (split-aware)\n", "- <PERSON><PERSON><PERSON> de K (Elbow, Silhouette, Gap Statistic)\n", "- <PERSON><PERSON><PERSON> híbrido (clustering + modelagem supervisionada)\n", "- Métricas com IC95% e interpretação de negócio\n"]}, {"cell_type": "code", "execution_count": null, "id": "c32aa386", "metadata": {}, "outputs": [], "source": ["\n", "from pathlib import Path\n", "import pandas as pd, numpy as np\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.cluster import KMeans\n", "from sklearn.metrics import silhouette_score\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "BASE = Path('.')\n", "REPORTS = BASE/'reports'/'2025-08-15'\n", "TABLES = REPORTS/'tables'\n", "PLOTS = REPORTS/'plots'\n", "PRESENT = PLOTS/'presentation'\n", "DATA = BASE/'data'/'processed'/'features_engineered_regional.csv'\n", "\n", "df = pd.read_csv(DATA, low_memory=False)\n", "print('Shape:', df.shape)\n"]}, {"cell_type": "markdown", "id": "abdffb2d", "metadata": {}, "source": ["\n", "## 1) Engenharia de atributos com anti-leakage (split-aware)\n", "\n", "- Seleção de colunas feita apenas no conjunto de treino (evita olhar o teste)\n", "- Remoção de colunas com correlação alta com o alvo e padrões suspeitos (ex.: `valor`, `store_avg_valor`, `UF_`, `sp_`, etc.)\n", "- Reuso do módulo tools/feature_filter.py\n"]}, {"cell_type": "code", "execution_count": null, "id": "e31fc51c", "metadata": {}, "outputs": [], "source": ["\n", "from tools.feature_filter import compute_clean_features\n", "\n", "TARGET = 'valor'\n", "# Split base (estratificação simples por quantis do alvo, se desejável)\n", "idx = np.arange(len(df))\n", "idx_tr, idx_te = train_test_split(idx, test_size=0.2, random_state=42)\n", "df_tr = df.iloc[idx_tr].copy(); df_te = df.iloc[idx_te].copy()\n", "\n", "clean_cols, audit_removed = compute_clean_features(\n", "    df_tr, TARGET, REPORTS,\n", "    corr_threshold=0.95,\n", "    suspicious_additional=['sp_', 'uf_', 'estado_', 'regiao_', 'loja_', 'franquia_']\n", ")\n", "print('N clean cols (train):', len(clean_cols))\n", "# Aplica mesma seleção no teste\n", "X_tr = df_tr[clean_cols].fillna(0.0)\n", "X_te = df_te[clean_cols].fillna(0.0)\n", "y_tr = df_tr[TARGET].values; y_te = df_te[TARGET].values\n", "\n", "scaler = StandardScaler()\n", "X_trs = scaler.fit_transform(X_tr)\n", "X_tes = scaler.transform(X_te)\n"]}, {"cell_type": "markdown", "id": "82fdfa84", "metadata": {}, "source": ["\n", "## 2) <PERSON><PERSON><PERSON> de K (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Gap Statistic)\n", "\n", "Avaliação para K=2..10, comparando o método atual (labels existentes) com K-Means.\n", "- Elbow: inércia do K-Means\n", "- Silhouette: qualidade de separação\n", "- Gap Statistic (aproximação bootstrap)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c5efa6f0", "metadata": {}, "outputs": [], "source": ["\n", "# Labels atuais (se existirem)\n", "assign_path = TABLES/'cluster_assignments.csv'\n", "if assign_path.exists():\n", "    lab = pd.read_csv(assign_path)\n", "    labels_current = lab['cluster'].values if 'cluster' in lab.columns else None\n", "else:\n", "    labels_current = None\n", "\n", "Xk = X_trs  # usa features de treino padronizadas para validação\n", "Ks = list(range(2,11))\n", "inertias = []\n", "sil_km = []\n", "for k in Ks:\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42)\n", "    km_labels = km.fit_predict(Xk)\n", "    inertias.append(float(km.inertia_))\n", "    sil_km.append(float(silhouette_score(Xk, km_labels)))\n", "\n", "sil_current = None\n", "if labels_current is not None and len(set(labels_current))>1:\n", "    # calcula silhouette do método atual nas mesmas features\n", "    sil_current = float(silhouette_score(Xk, labels_current[:len(Xk)]))\n", "\n", "fig, axes = plt.subplots(1,2, figsize=(12,4))\n", "axes[0].plot(Ks, inertias, marker='o'); axes[0].set_title('Elbow — Inércia K-Means'); axes[0].set_xlabel('K'); axes[0].set_ylabel('Inércia')\n", "axes[1].plot(Ks, sil_km, marker='o', label='K-Means')\n", "if sil_current is not None: axes[1].axhline(sil_current, color='orange', linestyle='--', label='Atual')\n", "axes[1].set_title('<PERSON>lhouette — K-Means vs <PERSON><PERSON>'); axes[1].set_xlabel('K'); axes[1].set_ylabel('Silhouette'); axes[1].legend()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "6a063887", "metadata": {}, "outputs": [], "source": ["\n", "# Gap Statistic (aproximação):\n", "# Para cada K, compara log(Wk) com log(Wk*) de amostras de referência (uniformes no espaço dos dados padronizados)\n", "import numpy as np\n", "rng = np.random.default_rng(42)\n", "B = 10  # número de amostras de referência (pode aumentar em execução offline)\n", "\n", "mins = Xk.min(axis=0); maxs = Xk.max(axis=0)\n", "\n", "def within_dispersion(X, labels):\n", "    # soma das distâncias quadráticas intra-cluster\n", "    s = 0.0\n", "    for c in np.unique(labels):\n", "        Xi = X[labels==c]\n", "        if len(Xi)==0: continue\n", "        mu = Xi.mean(axis=0)\n", "        s += float(((Xi - mu)**2).sum())\n", "    return s\n", "\n", "logWks = []\n", "logWkbs = []\n", "sks = []\n", "for k in Ks:\n", "    km = KMeans(n_clusters=k, n_init='auto', random_state=42).fit(Xk)\n", "    labels = km.labels_\n", "    Wk = within_dispersion(Xk, labels)\n", "    logWks.append(np.log(Wk))\n", "    # referência\n", "    ref_vals = []\n", "    for b in range(B):\n", "        Xref = rng.uniform(mins, maxs, size=Xk.shape)\n", "        kmr = KMeans(n_clusters=k, n_init='auto', random_state=42).fit(Xref)\n", "        Wkb = within_dispersion(Xref, kmr.labels_)\n", "        ref_vals.append(np.log(Wkb))\n", "    ref_vals = np.array(ref_vals)\n", "    logWkbs.append(ref_vals.mean())\n", "    sks.append(ref_vals.std()*np.sqrt(1****/B))\n", "\n", "gaps = np.array(logWkbs) - np.array(logWks)\n", "\n", "plt.figure(figsize=(6,4))\n", "plt.plot(Ks, gaps, marker='o'); plt.title('Gap Statistic'); plt.xlabel('K'); plt.ylabel('Gap')\n", "plt.show()\n", "\n", "# Regra de escolha (Tibshirani): menor K tal que Gap(K) >= Gap(K+1) - s_{K+1}\n", "chosen_K = Ks[np.argmax([gaps[i] - (gaps[i+1]-sks[i+1] if i < len(Ks)-1 else 0) for i in range(len(Ks))])]\n", "print('<PERSON> (regra Gap):', chosen_K)\n"]}, {"cell_type": "markdown", "id": "9b14df66", "metadata": {}, "source": ["\n", "## 3) Pipeline Híbrido: Clustering + Supervisionado\n", "\n", "- Clustering define segmentos; modelos supervisionados são treinados por segmento\n", "- Anti‑leakage: seleção de features no treino por cluster\n", "- Métricas por cluster com IC 95% e diagnósticos\n"]}, {"cell_type": "code", "execution_count": null, "id": "0572a96c", "metadata": {}, "outputs": [], "source": ["\n", "from tools.feature_filter import compute_clean_features\n", "from sklearn.model_selection import KFold\n", "\n", "# Exemplo simples: treinar regressão linear por cluster atual ou por K escolhido\n", "if assign_path.exists():\n", "    base_labels = labels_current[:len(df_tr)] if labels_current is not None else None\n", "else:\n", "    base_labels = None\n", "\n", "if base_labels is None:\n", "    # gera labels por K-Means com K escolhido\n", "    km_all = KMeans(n_clusters=int(chosen_K), n_init='auto', random_state=42)\n", "    base_labels = km_all.fit_predict(StandardScaler().fit_transform(df[clean_cols].fillna(0.0)))\n", "\n", "clusters = sorted(np.unique(base_labels))\n", "res = []\n", "for c in clusters:\n", "    idxc = np.where(base_labels==c)[0]\n", "    if len(idxc) < 60:\n", "        continue\n", "    dfc = df.iloc[idxc].copy()\n", "    y = dfc[TARGET].values\n", "    kf = KFold(n_splits=5, shuffle=True, random_state=42)\n", "    r2s=[]; rmses=[]; maes=[]\n", "    for tr, te in kf.split(dfc):\n", "        df_trc = dfc.iloc[tr].copy(); df_tec = dfc.iloc[te].copy()\n", "        clean_c, _ = compute_clean_features(df_trc, TARGET, REPORTS, corr_threshold=0.95,\n", "                                           suspicious_additional=['sp_','uf_','estado_','regiao_','loja_','franquia_'])\n", "        Xtr = df_trc[clean_c].fillna(0.0); Xte = df_tec[clean_c].fillna(0.0)\n", "        sc = StandardScaler(); Xtr = sc.fit_transform(Xtr); Xte = sc.transform(Xte)\n", "        m = LinearRegression().fit(Xtr, df_trc[TARGET])\n", "        yp = m.predict(Xte)\n", "        r2s.append(r2_score(df_tec[TARGET], yp))\n", "        rmses.append(mean_squared_error(df_tec[TARGET], yp, squared=False))\n", "        maes.append(mean_absolute_error(df_tec[TARGET], yp))\n", "    # IC95% (bootstrap normal approx.)\n", "    import numpy as np\n", "    def ci(arr):\n", "        arr = np.array(arr, dtype=float)\n", "        m = np.nanmean(arr); se = (np.nanstd(arr, ddof=1) / np.sqrt(max(1,len(arr))))\n", "        return m, m-1.96*se, m******se\n", "    r2m, r2l, r2h = ci(r2s); rmsem, rmsel, rmseh = ci(rmses); maem, mael, maeh = ci(maes)\n", "    res.append({'cluster': int(c), 'n': len(idxc), 'r2': r2m, 'r2_l': r2l, 'r2_h': r2h,\n", "                'rmse': rmsem, 'rmse_l': rmsel, 'rmse_h': rmseh,\n", "                'mae': maem, 'mae_l': mael, 'mae_h': maeh})\n", "\n", "res = pd.DataFrame(res)\n", "res.sort_values('r2', ascending=False).head()\n"]}, {"cell_type": "markdown", "id": "02f2a1d8", "metadata": {}, "source": ["\n", "## 4) Métricas com IC 95% e interpretação de negócio\n", "\n", "- KPIs principais: R² (qualidade explicativa), RMSE/MAE (erros)\n", "- IC 95% indica confiabilidade; Q‑Q plots ajudam a avaliar normalidade de resíduos\n", "- Interpretação: clusters com R² alto e erros baixos → priorização de alocação e campanhas; erros altos → revisar features/dados\n"]}, {"cell_type": "code", "execution_count": null, "id": "f695a941", "metadata": {}, "outputs": [], "source": ["\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "if not res.empty:\n", "    fig, axes = plt.subplots(1,3, figsize=(14,4))\n", "    o = res.sort_values('r2', ascending=False)\n", "    axes[0].bar(o['cluster'].astype(str), o['r2'])\n", "    axes[0].set_title('R² por Cluster'); axes[0].set_ylim(0,1)\n", "\n", "    axes[1].plot(o['cluster'].astype(str), o['rmse'], marker='o', label='RMSE')\n", "    axes[1].plot(o['cluster'].astype(str), o['mae'], marker='s', label='MAE')\n", "    axes[1].legend(); axes[1].set_title('<PERSON><PERSON><PERSON> por Cluster')\n", "\n", "    axes[2].axis('off')\n", "    axes[2].text(0,1, 'So What?:\n", "- Priorizar clusters fortes\n", "- Investigar clusters com erro alto\n", "- Usar K selecionado p/ estabilidade', va='top')\n", "    plt.show()\n", "else:\n", "    print('Sem resultados suficientes para plot.')\n"]}], "metadata": {}, "nbformat": 4, "nbformat_minor": 5}