from pathlib import Path
import nbformat as nbf

NB04 = Path('notebooks') / '04_territorial_analysis.ipynb'

cell_code = r'''# Recompute supervised-by-cluster using safe estimator (avoid column name mismatches)
import numpy as np, pandas as pd, matplotlib.pyplot as plt, seaborn as sns
from sklearn.model_selection import train_test_split, KFold, learning_curve
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.ensemble import RandomForestRegressor
from sklearn.svm import SVR
from sklearn.linear_model import LinearRegression

# Build safe estimator without ColumnTransformer (X already numeric)
def mk_estimator(name: str):
    if name in ['RF','GB','RandomForest']:
        base = RandomForestRegressor(n_estimators=300, random_state=42)
    elif name in ['SVM','SVR']:
        base = SVR(kernel='rbf')
    elif name in ['LR','Linear','LinearRegression']:
        base = LinearRegression()
    else:
        base = RandomForestRegressor(n_estimators=300, random_state=42)
    return Pipeline([('sc', StandardScaler(with_mean=False)), ('model', base)])

# Clean features list (ensure intersection with df columns)
if 'clean_num_cols' not in globals() or not clean_num_cols:
    clean_num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]
if target in clean_num_cols:
    clean_num_cols = [c for c in clean_num_cols if c != target]
clean_num_cols = [c for c in clean_num_cols if c in df.columns]

metrics_rows3 = []
all_preds3 = []
clusters = sorted(pd.Series(cluster_labels).dropna().unique()) if 'cluster_labels' in globals() else []
for c_id in clusters:
    mask = (pd.Series(cluster_labels)==c_id).values
    dfc = df.loc[mask]
    if len(dfc) < 120: continue
    X = dfc[clean_num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)
    Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)
    est = mk_estimator(str(best_model_name))
    est.fit(Xtr, ytr)
    yp = est.predict(Xte)
    r2 = float(r2_score(yte, yp)); rmse = float(np.sqrt(mean_squared_error(yte, yp))); mae = float(mean_absolute_error(yte, yp))
    metrics_rows3.append({'cluster':int(c_id),'n':int(len(dfc)),'r2':r2,'rmse':rmse,'mae':mae})
    all_preds3.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))

if metrics_rows3:
    mdf3 = pd.DataFrame(metrics_rows3).sort_values('cluster')
    mdf3.to_csv(TABLES/'supervised_by_cluster_metrics.csv', index=False)
    fig, axes = plt.subplots(1,3, figsize=(14,4))
    sns.barplot(data=mdf3, x='cluster', y='r2', ax=axes[0]); axes[0].set_title('R² por Cluster');
    sns.barplot(data=mdf3, x='cluster', y='rmse', ax=axes[1]); axes[1].set_title('RMSE por Cluster');
    sns.barplot(data=mdf3, x='cluster', y='mae', ax=axes[2]); axes[2].set_title('MAE por Cluster');
    plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_performance.png', dpi=180); plt.show()

if all_preds3:
    preds3 = pd.concat(all_preds3, ignore_index=True)
    preds3.to_csv(TABLES/'supervised_by_cluster_predictions.csv', index=False)
    plt.figure(figsize=(6,6));
    sns.scatterplot(data=preds3, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)
    lim = (min(preds3['y_true'].min(), preds3['y_pred'].min()), max(preds3['y_true'].max(), preds3['y_pred'].max()))
    plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_predictions.png', dpi=180); plt.show()
'''

def main():
    nb = nbf.read(NB04, as_version=4)
    nb.cells.append(nbf.v4.new_code_cell(cell_code))
    nbf.write(nb, NB04)
    print('Appended safe training cell to 04')

if __name__ == '__main__':
    main()

