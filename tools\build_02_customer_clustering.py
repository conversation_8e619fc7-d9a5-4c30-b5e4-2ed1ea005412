import nbformat as nbf
from pathlib import Path

md = lambda t: nbf.v4.new_markdown_cell(t)
code = lambda t: nbf.v4.new_code_cell(t)

BASE = Path(__file__).resolve().parents[1]
NB_PATH = BASE / 'notebooks' / '02_customer_clustering.ipynb'

nb = nbf.v4.new_notebook()

nb.cells.append(md(
    "# Customer Clustering – ChilliAnalyzer MVP (Ricardo)\n\n"
    "Objetivo: segmentar clientes em 4–6 perfis e estimar propensão de compra por segmento."
))

# 1. Setup
nb.cells.append(md('## 1. Setup'))
nb.cells.append(code(
    "from pathlib import Path\n"
    "import pandas as pd, numpy as np\n"
    "import matplotlib.pyplot as plt, seaborn as sns\n"
    "from sklearn.preprocessing import StandardScaler\n"
    "from sklearn.decomposition import PCA\n"
    "from sklearn.cluster import KMeans\n"
    "from sklearn.metrics import silhouette_score\n"
    "from sklearn.model_selection import StratifiedKFold, cross_val_score\n"
    "from sklearn.linear_model import LogisticRegression\n"
    "import plotly.express as px\n"
    "import warnings; warnings.filterwarnings('ignore')\n"
    "BASE = Path('.')\n"
    "if not (BASE/'data'/'processed'/'features_engineered.csv').exists(): BASE = Path('..')\n"
    "DATA = BASE/'data'/'processed'/'features_engineered.csv'\n"
    "REPORTS = BASE/'reports'/'2025-08-15'/'customer_clustering'\n"
    "MODELS = BASE/'models'\n"
    "for d in [REPORTS, MODELS]: d.mkdir(parents=True, exist_ok=True)\n"
))

# 2. Load & feature selection (demographics + behavior)
nb.cells.append(md('## 2. Carregamento e seleção de features (demografia + comportamento)'))
nb.cells.append(code(
    "df = pd.read_csv(DATA, low_memory=False)\n"
    "# Heurísticas para colunas demográficas e comportamentais\n"
    "demo_keys = ['idade','genero','sexo','uf','cidade','renda','segmento']\n"
    "beh_keys = ['qtd','valor','frequencia','ticket_medio']\n"
    "cols = [c for c in df.columns if any(k in c.lower() for k in demo_keys+beh_keys)]\n"
    "# Remover IDs\n"
    "cols = [c for c in cols if not (c.lower().startswith('id_') or c.lower().endswith('_id') or c.lower() in ['id','id_cliente','id_loja','id_produto','id_vendedor'])]\n"
    "X = df[cols].select_dtypes(include=['number']).fillna(df[cols].select_dtypes(include=['number']).median())\n"
    "print('X shape:', X.shape)\n"
))

# 3. Scaling + PCA
nb.cells.append(md('## 3. Escalonamento e PCA'))
nb.cells.append(code(
    "sc = StandardScaler()\n"
    "Xsc = sc.fit_transform(X)\n"
    "pca = PCA(n_components=2, random_state=42)\n"
    "Xp = pca.fit_transform(Xsc)\n"
    "fig = px.scatter(x=Xp[:,0], y=Xp[:,1], title='PCA (2D) – Clientes')\n"
    "fig.write_html(str(REPORTS/'pca_scatter.html'))\n"
))

# 4. K-means + K selection
nb.cells.append(md('## 4. K-means e seleção de K')))
nb.cells.append(code(
    "best_k, best_sil = None, -1\n"
    "for k in range(4,7):\n"
    "    km = KMeans(n_clusters=k, random_state=42, n_init='auto')\n"
    "    labels = km.fit_predict(Xsc)\n"
    "    sil = silhouette_score(Xsc, labels)\n"
    "    if sil > best_sil: best_sil, best_k = sil, k\n"
    "print('Melhor K:', best_k, 'silhouette=', round(best_sil,3))\n"
    "km = KMeans(n_clusters=best_k, random_state=42, n_init='auto').fit(Xsc)\n"
    "labels = km.labels_\n"
    "pd.Series(labels).to_csv(REPORTS/'cluster_labels.csv', index=False)\n"
))

# 5. Propensão por cluster (classificação simples como proxy)
nb.cells.append(md('## 5. Propensão por cluster'))
nb.cells.append(code(
    "# Como proxy, definimos target binário por tercis de valor\n"
    "y = (df['valor'] > df['valor'].quantile(0.66)).astype(int) if 'valor' in df.columns else (labels%2)\n"
    "clf = LogisticRegression(max_iter=200, random_state=42)\n"
    "from sklearn.model_selection import StratifiedKFold\n"
    "cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)\n"
    "scores = cross_val_score(clf, Xsc, y, cv=cv, scoring='f1')\n"
    "print('F1 médio:', round(scores.mean(),3), '+-', round(scores.std(),3))\n"
    "clf.fit(Xsc, y)\n"
    "import joblib\n"
    "joblib.dump({'scaler': sc, 'pca': pca, 'kmeans': km, 'clf': clf}, BASE/'models'/'customer_clustering.pkl')\n"
))

# 6. Outputs e recomendações
nb.cells.append(md('## 6. Saídas e recomendações'))
nb.cells.append(code(
    "desc = pd.DataFrame(X).groupby(labels).agg(['mean','median'])\n"
    "desc.to_csv(REPORTS/'cluster_profiles.csv')\n"
    "# Estratégias de comunicação (placeholder orientativo)\n"
    "strategies = pd.DataFrame({'cluster': range(best_k), 'estrategia': ['promoção digital','bundle','upsell','fidelização','geo-oferta','parcerias'][:best_k]})\n"
    "strategies.to_csv(REPORTS/'strategies.csv', index=False)\n"
))

# Write notebook
NB_PATH.parent.mkdir(parents=True, exist_ok=True)
with open(NB_PATH, 'w', encoding='utf-8') as f:
    nbf.write(nb, f)
print('Notebook built at', NB_PATH)

