from pathlib import Path
import nbformat as nbf

BASE = Path('.')
NB_DIR = BASE / 'notebooks'


def ensure_dirs_code() -> str:
    return (
        "# Setup paths and output dirs\n"
        "from pathlib import Path\n"
        "BASE = Path('.')\n"
        "REPORTS = BASE / 'reports' / '2025-08-15'\n"
        "PLOTS = REPORTS / 'plots'\n"
        "TABLES = REPORTS / 'tables'\n"
        "for d in [PLOTS, TABLES]: d.mkdir(parents=True, exist_ok=True)\n"
        "print('Using REPORTS:', REPORTS)\n"
    )


def inject_cells(nb_path: Path, new_cells: list[str]) -> None:
    nb = nbf.read(nb_path, as_version=4)
    for code in new_cells:
        nb['cells'].append(nbf.v4.new_code_cell(code))
    nbf.write(nb, nb_path)
    print('Injected', len(new_cells), 'cells into', nb_path)


def enhance_05_customer_segmentation():
    nb = NB_DIR / '05_customer_segmentation.ipynb'
    if not nb.exists():
        print('WARN: missing notebook', nb)
        return
    cells = []
    cells.append(ensure_dirs_code())
    cells.append(
        "# Load data (fallback to regional if needed)\n"
        "import pandas as pd, numpy as np\n"
        "DATA1 = BASE / 'data' / 'processed' / 'features_engineered.csv'\n"
        "DATA2 = BASE / 'data' / 'processed' / 'features_engineered_regional.csv'\n"
        "DATA = DATA1 if DATA1.exists() else DATA2\n"
        "df = pd.read_csv(DATA, low_memory=False)\n"
        "print('Loaded', DATA, 'shape=', df.shape)\n"
    )
    cells.append(
        "# Select numeric features and sample\n"
        "num_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n"
        "X = df[num_cols].copy()\n"
        "from sklearn.preprocessing import StandardScaler\n"
        "sc = StandardScaler(with_mean=False)\n"
        "X_sc = sc.fit_transform(X)\n"
        "import scipy.sparse as sp\n"
        "if not sp.issparse(X_sc):\n"
        "    import numpy as np\n"
        "    from scipy import sparse\n"
        "    X_sc = sparse.csr_matrix(X_sc)\n"
        "from sklearn.utils import resample\n"
        "n = X_sc.shape[0]\n"
        "sample_n = min(20000, n)\n"
        "idx = np.random.RandomState(42).choice(n, sample_n, replace=False)\n"
        "X_sub = X_sc[idx]\n"
        "print('Clustering on sample of', X_sub.shape)\n"
    )
    cells.append(
        "# KMeans sweep K=2..8 with metrics\n"
        "from sklearn.cluster import KMeans\n"
        "from sklearn.metrics import silhouette_score, davies_bouldin_score\n"
        "import pandas as pd, numpy as np\n"
        "metrics = []\n"
        "for k in range(2,9):\n"
        "    km = KMeans(n_clusters=k, random_state=42, n_init=10)\n"
        "    labels = km.fit_predict(X_sub)\n"
        "    try:\n"
        "        sil = float(silhouette_score(X_sub, labels, metric='euclidean'))\n"
        "    except Exception:\n"
        "        sil = float('nan')\n"
        "    try:\n"
        "        dbi = float(davies_bouldin_score(X_sub.toarray() if hasattr(X_sub,'toarray') else X_sub, labels))\n"
        "    except Exception:\n"
        "        dbi = float('nan')\n"
        "    inertia = float(km.inertia_)\n"
        "    metrics.append({'k':k,'silhouette':sil,'davies_bouldin':dbi,'inertia':inertia})\n"
        "metrics_df = pd.DataFrame(metrics)\n"
        "metrics_df.to_csv(TABLES / 'clustering_metrics.csv', index=False)\n"
        "metrics_df\n"
    )
    cells.append(
        "# Plots: inertia elbow, silhouette, DB index\n"
        "import matplotlib.pyplot as plt, seaborn as sns\n"
        "plt.figure(figsize=(6,4)); sns.lineplot(data=metrics_df, x='k', y='inertia', marker='o'); plt.title('Elbow (Inertia)'); plt.tight_layout(); plt.savefig(PLOTS/'clustering_elbow.png', dpi=180); plt.show()\n"
        "plt.figure(figsize=(6,4)); sns.barplot(data=metrics_df, x='k', y='silhouette'); plt.title('Silhouette por K'); plt.tight_layout(); plt.savefig(PLOTS/'clustering_silhouette.png', dpi=180); plt.show()\n"
        "plt.figure(figsize=(6,4)); sns.barplot(data=metrics_df, x='k', y='davies_bouldin'); plt.title('Davies-Bouldin por K (menor é melhor)'); plt.tight_layout(); plt.savefig(PLOTS/'clustering_dbindex.png', dpi=180); plt.show()\n"
    )
    cells.append(
        "# Choose best K (max silhouette, tie -> min DB) and visualize PCA scatter\n"
        "best_k = int(metrics_df.sort_values(['silhouette', 'davies_bouldin'], ascending=[False, True]).iloc[0]['k'])\n"
        "km = KMeans(n_clusters=best_k, random_state=42, n_init=10)\n"
        "labels = km.fit_predict(X_sub)\n"
        "pd.Series(labels).value_counts().rename('count').to_csv(TABLES/'cluster_sizes.csv')\n"
        "from sklearn.decomposition import PCA\n"
        "try:\n"
        "    pca = PCA(n_components=2, random_state=42)\n"
        "    # convert to dense small array for PCA\n"
        "    Xd = X_sub.toarray() if hasattr(X_sub, 'toarray') else X_sub\n"
        "    XY = pca.fit_transform(Xd)\n"
        "    import matplotlib.pyplot as plt\n"
        "    plt.figure(figsize=(6,5));\n"
        "    sns.scatterplot(x=XY[:,0], y=XY[:,1], hue=labels, palette='tab10', s=12)\n"
        "    plt.title(f'Clusters (PCA 2D) — K={best_k}')\n"
        "    plt.tight_layout(); plt.savefig(PLOTS/f'clustering_pca_k{best_k}.png', dpi=180); plt.show()\n"
        "except Exception as e:\n"
        "    print('PCA plot skipped:', e)\n"
    )
    cells.append(
        "# Fit clustering on full data (SVD->KMeans) and export assignments\n"
        "from sklearn.decomposition import TruncatedSVD\n"
        "from sklearn.cluster import KMeans\n"
        "try:\n"
        "    svd = TruncatedSVD(n_components=min(20, X_sc.shape[1]-1) if X_sc.shape[1]>1 else 1, random_state=42)\n"
        "    X_red = svd.fit_transform(X_sc)\n"
        "    try:\n"
        "        k_opt = int(best_k) if 'best_k' in globals() else 5\n"
        "    except Exception:\n"
        "        k_opt = 5\n"
        "    km_full = KMeans(n_clusters=k_opt, random_state=42, n_init=10)\n"
        "    labels_full = km_full.fit_predict(X_red)\n"
        "    pd.DataFrame({'index': np.arange(len(labels_full)), 'cluster': labels_full}).to_csv(TABLES/'cluster_assignments.csv', index=False)\n"
        "    print('Exported cluster assignments for full dataset ->', TABLES/'cluster_assignments.csv')\n"
        "except Exception as e:\n"
        "    print('WARN: full-data clustering export failed ->', e)\n"
    )

    inject_cells(nb, cells)


def enhance_06_business_reporting():
    nb = NB_DIR / '06_business_insights_reporting.ipynb'
    if not nb.exists():
        print('WARN: missing notebook', nb)
        return
    cells = []
    cells.append(ensure_dirs_code())
    cells.append(
        "# Load algorithm ranking and build executive visuals\n"
        "import pandas as pd, numpy as np; import matplotlib.pyplot as plt, seaborn as sns\n"
        "rank_path = (Path('reports')/'2025-08-15'/'tables'/'algorithm_ranking.csv')\n"
        "df = pd.read_csv(rank_path) if rank_path.exists() else pd.DataFrame()\n"
        "print('Algorithm ranking exists:', rank_path.exists())\n"
        "if not df.empty:\n"
        "    # Multi-metric bars\n"
        "    for metric in ['r2_mean','rmse_mean','mae_mean']:\n"
        "        if metric in df.columns:\n"
        "            plt.figure(figsize=(7,4));\n"
        "            order = df.sort_values(metric, ascending=(metric!='r2_mean'))['model']\n"
        "            sns.barplot(data=df, x='model', y=metric, order=order)\n"
        "            plt.title(f'{metric.replace("_mean","").upper()} por modelo')\n"
        "            plt.tight_layout(); plt.savefig(Path('reports')/'2025-08-15'/'plots'/f'bar_{metric}.png', dpi=180); plt.show()\n"
        "    # Ranking table export (por RMSE)\n"
        "    asc = True if 'rmse_mean' in df.columns else False\n"
        "    out = df.sort_values('rmse_mean', ascending=asc) if 'rmse_mean' in df.columns else df\n"
        "    out.to_csv(Path('reports')/'2025-08-15'/'tables'/'algorithm_ranking_sorted.csv', index=False)\n"
    )
    cells.append(
        "# Regional comparisons (if present)\n"
        "comp_dir = Path('reports')/'2025-08-15'/'comparisons'\n"
        "perf_path = comp_dir/'regional_model_performance.csv'\n"
        "if perf_path.exists():\n"
        "    perf = pd.read_csv(perf_path)\n"
        "    if not perf.empty:\n"
        "        plt.figure(figsize=(8,5)); sns.barplot(data=perf, x='model', y='r2', hue='region'); plt.title('R2 — SP vs Resto'); plt.tight_layout(); plt.savefig(Path('reports')/'2025-08-15'/'plots'/'regional_r2_side_by_side.png', dpi=180); plt.show()\n"
        "        plt.figure(figsize=(8,5)); sns.barplot(data=perf, x='model', y='rmse', hue='region'); plt.title('RMSE — SP vs Resto'); plt.tight_layout(); plt.savefig(Path('reports')/'2025-08-15'/'plots'/'regional_rmse_side_by_side.png', dpi=180); plt.show()\n"
    )
    inject_cells(nb, cells)


    # Fallback ensure map exists even without UF
    code2 = (
        "# Ensure territorial_clustering_map.png exists (fallback using REGIAO_CHILLI one-hots or simple cluster size bar)\n"
        "from pathlib import Path\n"
        "import numpy as np, pandas as pd\n"
        "import matplotlib.pyplot as plt, seaborn as sns\n"
        "PLOTS = (Path('.') if (Path('.')/'reports').exists() else Path('..'))/'reports'/'2025-08-15'/'plots'\n"
        "map_path = PLOTS/'territorial_clustering_map.png'\n"
        "PLOTS.mkdir(parents=True, exist_ok=True)\n"
        "try:\n"
        "    need = not map_path.exists()\n"
        "except Exception:\n"
        "    need = True\n"
        "if need:\n"
        "    region_oh = [c for c in df.columns if 'REGIAO_CHILLI' in c]\n"
        "    if region_oh:\n"
        "        vals = pd.DataFrame(df[region_oh].values, columns=region_oh)\n"
        "        idx = np.argmax(vals.values, axis=1)\n"
        "        cats = [region_oh[i] for i in idx]\n"
        "        cat_series = pd.Series(cats, index=df.index, name='territory')\n"
        "        order = sorted(list(pd.unique(cat_series)))\n"
        "        y = np.random.RandomState(42).rand(len(df))\n"
        "        plt.figure(figsize=(12,6))\n"
        "        sns.scatterplot(x=pd.Categorical(cat_series, categories=order, ordered=True), y=y, hue=cluster_labels, palette='tab10', s=6, alpha=0.5, legend=False)\n"
        "        sizes = pd.Series(cluster_labels).value_counts().sort_index()\n"
        "        handles = [plt.Line2D([0],[0], marker='o', color='w', label=f'Cluster {i} (n={sizes.get(i,0)})', markerfacecolor=plt.cm.tab10(i%10), markersize=8) for i in range(int(pd.Series(cluster_labels).max())+1)]\n"
        "        plt.legend(handles=handles, title='Clusters', bbox_to_anchor=(1.02,1), loc='upper left')\n"
        "        plt.title('Segmentação Territorial por Região (REGIAO_CHILLI) — clusters')\n"
        "        plt.xlabel('Região'); plt.ylabel('pos. relativa (visual)'); plt.tight_layout(); plt.savefig(map_path, dpi=180, bbox_inches='tight'); plt.show()\n"
        "    else:\n"
        "        sizes = pd.Series(cluster_labels).value_counts().sort_index()\n"
        "        plt.figure(figsize=(8,4)); sns.barplot(x=sizes.index, y=sizes.values)\n"
        "        plt.title('Tamanho dos clusters (fallback)'); plt.xlabel('Cluster'); plt.ylabel('n'); plt.tight_layout(); plt.savefig(map_path, dpi=180); plt.show()\n"
    )
    nb_obj['cells'].append(nbf.v4.new_code_cell(code2))
    nbf.write(nb_obj, NB_DIR / '04_territorial_analysis.ipynb')
    print('Injected territorial analysis cells into 04_territorial_analysis.ipynb')

def enhance_04_territorial_analysis():
    nb = NB_DIR / '04_territorial_analysis.ipynb'
    if not nb.exists():
        print('WARN: missing notebook', nb)
        return
    md = (
        "## Visualizações de Clusterização Territorial (não supervisionado) e Performance Supervisionada por Cluster\n\n"
        "Esta seção integra os resultados de segmentação (Notebook 05) com a modelagem supervisionada (Notebook 03) para: "
        "(1) visualizar como o algoritmo não supervisionado particiona o território; e (2) avaliar o desempenho do melhor modelo supervisionado por cluster territorial.\n"
    )
    code = (
        "# Two-stage territorial visualization with robust fallbacks\n"
        "from pathlib import Path\nimport pandas as pd, numpy as np\nimport matplotlib.pyplot as plt, seaborn as sns\nfrom sklearn.model_selection import train_test_split\nfrom sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\nfrom sklearn.preprocessing import OneHotEncoder, StandardScaler\nfrom sklearn.compose import ColumnTransformer\nfrom sklearn.pipeline import Pipeline\nfrom sklearn.ensemble import RandomForestRegressor\nfrom sklearn.linear_model import LinearRegression\nfrom sklearn.svm import SVR\nimport warnings; warnings.filterwarnings('ignore')\n\nBASE = Path('.')\nif not (BASE/'data'/'processed'/'features_engineered_regional.csv').exists(): BASE = Path('..')\nDATA = BASE/'data'/'processed'/'features_engineered_regional.csv' if (BASE/'data'/'processed'/'features_engineered_regional.csv').exists() else BASE/'data'/'processed'/'features_engineered.csv'\nREPORTS = BASE/'reports'/'2025-08-15'\nPLOTS = REPORTS/'plots'\nTABLES = REPORTS/'tables'\nPLOTS.mkdir(parents=True, exist_ok=True); TABLES.mkdir(parents=True, exist_ok=True)\n\n# Load main data\ndf = pd.read_csv(DATA, low_memory=False)\nprint('Loaded data:', DATA, df.shape)\n\n# 1) Load clustering labels from notebook 05 if available\nclust_assign_path = TABLES/'cluster_assignments.csv'\ncluster_labels = None\nif clust_assign_path.exists():\n    try:\n        cl = pd.read_csv(clust_assign_path)\n        # Expect a column 'cluster' and optional 'index' to align\n        if 'index' in cl.columns: cl = cl.set_index('index').sort_index()\n        if len(cl)>=len(df):\n            cluster_labels = cl['cluster'].iloc[:len(df)].values\n        else:\n            cluster_labels = cl['cluster'].reindex(range(len(df))).values\n        print('Loaded clustering assignments from', clust_assign_path)\n    except Exception as e:\n        print('WARN: failed to load cluster assignments ->', e)\n\n# Fallback: build proxy clusters from available territorial features if needed\nif cluster_labels is None:\n    try:\n        # Use UF and one-hots of REGIAO_CHILLI as territorial signals\n        territorial_cols = []\n        if 'UF' in df.columns: territorial_cols.append('UF')\n        territorial_cols += [c for c in df.columns if 'REGIAO_CHILLI' in c]\n        X_terr = df[territorial_cols].copy() if territorial_cols else pd.DataFrame(index=df.index)\n        # Encode UF if present\n        transformers=[]\n        if 'UF' in X_terr.columns:\n            transformers.append(('uf_ohe', OneHotEncoder(handle_unknown='ignore'), ['UF']))\n        fixed = [c for c in X_terr.columns if c!='UF']\n        if fixed:\n            transformers.append(('pass', 'passthrough', fixed))\n        if not transformers:\n            # As last resort, use first few numeric cols for clustering proxy (not saved, only for viz split)\n            num_cols=[c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n            X_terr = df[num_cols[:8]].copy(); transformers=[('sc','passthrough', X_terr.columns.tolist())]\n        pre = ColumnTransformer(transformers)\n        X_enc = pre.fit_transform(X_terr)\n        from sklearn.cluster import KMeans\n        k = 5\n        km = KMeans(n_clusters=k, random_state=42, n_init=10)\n        labels = km.fit_predict(X_enc)\n        cluster_labels = labels\n        print('Built proxy clusters (k=5) from territorial signals')\n    except Exception as e:\n        print('ERROR: unable to construct proxy clusters ->', e)\n        cluster_labels = np.zeros(len(df), dtype=int)\n\n# 1) Visualization: \"map\" (UF strip) or scatter by UF/color cluster\ntry:\n    if 'UF' in df.columns:\n        order = sorted(df['UF'].dropna().unique())\n        uf_cat = pd.Categorical(df['UF'], categories=order, ordered=True)\n        # Plot jittered strip per UF colored by cluster\n        plt.figure(figsize=(12,6))\n        y = np.random.RandomState(42).rand(len(df))\n        sns.scatterplot(x=uf_cat, y=y, hue=cluster_labels, palette='tab10', s=6, alpha=0.5, legend=False)\n        # Legend with cluster sizes\n        sizes = pd.Series(cluster_labels).value_counts().sort_index()\n        handles = [plt.Line2D([0],[0], marker='o', color='w', label=f'Cluster {i} (n={sizes.get(i,0)})', markerfacecolor=plt.cm.tab10(i%10), markersize=8) for i in range(int(pd.Series(cluster_labels).max())+1)]\n        plt.legend(handles=handles, title='Clusters', bbox_to_anchor=(1.02,1), loc='upper left')\n        plt.title('Segmentação Territorial por UF (cores=clusters)')\n        plt.xlabel('UF'); plt.ylabel('pos. relativa (visual)'); plt.tight_layout();\n        plt.savefig(PLOTS/'territorial_clustering_map.png', dpi=180, bbox_inches='tight'); plt.show()\n    else:\n        print('UF column not found; skipping territorial map')\nexcept Exception as e:\n    print('WARN: failed to build territorial clustering map ->', e)\n\n# 2) Supervised model performance by cluster\n# Determine best model from algorithm ranking (03)\nrank_path = TABLES/'algorithm_ranking.csv'\nbest_model_name = None\nif rank_path.exists():\n    try:\n        rank = pd.read_csv(rank_path)\n        if 'rmse_mean' in rank.columns:\n            best_model_name = rank.sort_values('rmse_mean').iloc[0]['model']\n        elif 'r2_mean' in rank.columns:\n            best_model_name = rank.sort_values('r2_mean', ascending=False).iloc[0]['model']\n        print('Best model from ranking:', best_model_name)\n    except Exception as e:\n        print('WARN: could not read ranking ->', e)\n\n# Build a simple training pipeline for the best model (fallback to RF)\nnum_cols = [c for c in df.columns if pd.api.types.is_numeric_dtype(df[c])]\n# Candidate targets in likely priority\ncandidates = [c for c in ['valor','sq_valor','y','sales','revenue'] if c in df.columns]\ntarget = candidates[0] if candidates else (num_cols[0] if num_cols else None)\nif target in num_cols:\n    num_cols = [c for c in num_cols if c!=target]\n\nif best_model_name is None:\n    best_model_name = 'RF'\n\ndef make_model(name:str):\n    if name in ['RF','GB','RandomForest']:\n        base = RandomForestRegressor(n_estimators=300, random_state=42)\n    elif name in ['SVM','SVR']:\n        base = SVR(kernel='rbf')\n    elif name in ['LR','Linear','LinearRegression']:\n        base = LinearRegression()\n    else:\n        base = RandomForestRegressor(n_estimators=300, random_state=42)\n    pre = ColumnTransformer([\n        ('num', StandardScaler(with_mean=False), num_cols)\n    ], remainder='drop')\n    return Pipeline([('pre', pre), ('model', base)])\n\nmetrics_rows = []\nall_preds = []\nif target is None or not num_cols:\n    print('ERROR: target or numeric features not available; skipping supervised by cluster')\nelse:\n    for c_id in sorted(pd.Series(cluster_labels).dropna().unique()):\n        mask = (pd.Series(cluster_labels)==c_id).values\n        dfc = df.loc[mask]\n        if len(dfc) < 100:\n            print(f'Cluster {c_id}: too few samples ({len(dfc)}), skipping metrics')\n            continue\n        X = dfc[num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n        Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.2, random_state=42)\n        pipe = make_model(str(best_model_name))\n        try:\n            pipe.fit(Xtr, ytr)\n            yp = pipe.predict(Xte)\n            r2 = r2_score(yte, yp)\n            rmse = float(np.sqrt(mean_squared_error(yte, yp)))\n            mae = float(mean_absolute_error(yte, yp))\n            metrics_rows.append({'cluster':int(c_id),'n':len(dfc),'r2':r2,'rmse':rmse,'mae':mae})\n            all_preds.append(pd.DataFrame({'cluster':int(c_id),'y_true':yte.values,'y_pred':yp}))\n        except Exception as e:\n            print(f'Cluster {c_id}: training failed ->', e)\n\n    if metrics_rows:\n        mdf = pd.DataFrame(metrics_rows).sort_values('cluster')\n        mdf.to_csv(TABLES/'supervised_by_cluster_metrics.csv', index=False)\n        # Combined panel: R2, RMSE, MAE\n        fig, axes = plt.subplots(1,3, figsize=(14,4))\n        sns.barplot(data=mdf, x='cluster', y='r2', ax=axes[0]); axes[0].set_title('R² por Cluster');\n        sns.barplot(data=mdf, x='cluster', y='rmse', ax=axes[1]); axes[1].set_title('RMSE por Cluster');\n        sns.barplot(data=mdf, x='cluster', y='mae', ax=axes[2]); axes[2].set_title('MAE por Cluster');\n        plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_performance.png', dpi=180); plt.show()\n\n    if all_preds:\n        pd.concat(all_preds, ignore_index=True).to_csv(TABLES/'supervised_by_cluster_predictions.csv', index=False)\n        # Scatter predicted vs actual colored by cluster\n        preds = pd.concat(all_preds, ignore_index=True)\n        plt.figure(figsize=(6,6));\n        sns.scatterplot(data=preds, x='y_true', y='y_pred', hue='cluster', palette='tab10', s=12, alpha=0.6)\n        lim = (min(preds['y_true'].min(), preds['y_pred'].min()), max(preds['y_true'].max(), preds['y_pred'].max()))\n        plt.plot(lim, lim, 'k--', lw=1); plt.xlabel('Real'); plt.ylabel('Previsto'); plt.title('Predito vs Real por Cluster'); plt.tight_layout(); plt.savefig(PLOTS/'supervised_by_cluster_predictions.png', dpi=180); plt.show()\n\n    # Feature importances per cluster (if supported)\n    try:\n        if str(best_model_name) in ['RF','GB','RandomForest'] and num_cols:\n            imp_rows = []\n            for c_id in sorted(pd.Series(cluster_labels).dropna().unique()):\n                mask = (pd.Series(cluster_labels)==c_id).values\n                dfc = df.loc[mask]\n                if len(dfc) < 200: continue\n                X = dfc[num_cols].copy(); y = pd.to_numeric(dfc[target], errors='coerce').fillna(0.0)\n                pipe = make_model('RF'); pipe.fit(X, y)\n                model = pipe.named_steps['model']\n                if hasattr(model, 'feature_importances_'):\n                    imps = model.feature_importances_\n                    top_idx = np.argsort(imps)[-10:][::-1]\n                    out = pd.DataFrame({'feature':[num_cols[i] for i in top_idx], 'importance':[float(imps[i]) for i in top_idx]})\n                    out['cluster']=int(c_id)\n                    out.to_csv(TABLES/f'feature_importances_top10_cluster_{int(c_id)}.csv', index=False)\n    except Exception as e:\n        print('Feature importance by cluster skipped ->', e)\n"
    )
    nb_obj = nbf.read(nb, as_version=4)
    nb_obj['cells'].append(nbf.v4.new_markdown_cell(md))
    nb_obj['cells'].append(nbf.v4.new_code_cell(code))
    nbf.write(nb_obj, NB_DIR / '04_territorial_analysis.ipynb')
    print('Injected territorial analysis cells into 04_territorial_analysis.ipynb')



if __name__ == '__main__':
    enhance_05_customer_segmentation()
    enhance_06_business_reporting()
    enhance_04_territorial_analysis()
    print('Notebook augmentations complete.')

